# GameFlex Multi-Environment AWS Setup

This document describes the multi-environment setup for the GameFlex AWS backend, supporting Development, QA, Staging, and Production environments.

## Overview

The GameFlex backend now supports four distinct environments:

- **Development**: LocalStack-based local development environment
- **QA**: Real AWS environment for quality assurance testing
- **Staging**: Real AWS environment for pre-production testing
- **Production**: Real AWS environment for live production workloads

## Environment Configuration

### Directory Structure

```
aws-backend/
├── cloudformation/
│   ├── parameters/
│   │   ├── development.json      # Development parameters
│   │   ├── qa.json              # QA parameters
│   │   ├── staging.json         # Staging parameters
│   │   └── production.json      # Production parameters
│   ├── gameflex-infrastructure.yaml           # LocalStack template
│   ├── gameflex-simple-infrastructure.yaml    # LocalStack simple template
│   └── gameflex-production-infrastructure.yaml # Production template
├── .env.development             # Development environment variables
├── .env.qa                     # QA environment variables
├── .env.staging                # Staging environment variables
├── .env.production             # Production environment variables
├── deploy-infrastructure.ps1   # Universal deployment script
├── deploy-qa.ps1              # QA-specific deployment
├── deploy-staging.ps1          # Staging-specific deployment
├── deploy-production.ps1       # Production-specific deployment
└── manage-environments.ps1     # Environment management utility
```

### Environment Characteristics

| Environment | AWS Type | Domain | Database | Monitoring | Security |
|-------------|----------|--------|----------|------------|----------|
| Development | LocalStack | localhost | LocalStack RDS | Basic | Minimal |
| QA | Real AWS | qa.gameflex.io | RDS t3.small | Enhanced | Standard |
| Staging | Real AWS | staging.gameflex.io | RDS t3.medium | Full | Enhanced |
| Production | Real AWS | gameflex.io | RDS t3.large | Full | Maximum |

## Quick Start

### 1. Environment Management

Use the environment management script to check status and manage environments:

```powershell
# List all environments and their status
.\manage-environments.ps1

# Validate a specific environment configuration
.\manage-environments.ps1 -Action validate -Environment qa

# Deploy an environment
.\manage-environments.ps1 -Action deploy -Environment staging
```

### 2. Development Environment

For local development using LocalStack:

```powershell
# Start LocalStack services
.\start.ps1

# Deploy development infrastructure
.\deploy-infrastructure.ps1 -Environment development

# Or use the management script
.\manage-environments.ps1 -Action deploy -Environment development
```

### 3. QA Environment

For QA testing on real AWS:

```powershell
# Configure AWS credentials for QA
aws configure --profile qa

# Update QA parameters file with real values
# Edit: cloudformation/parameters/qa.json

# Deploy QA infrastructure
.\deploy-qa.ps1 -AwsProfile qa

# Or use the management script
.\manage-environments.ps1 -Action deploy -Environment qa -AwsProfile qa
```

### 4. Staging Environment

For staging/pre-production testing:

```powershell
# Configure AWS credentials for staging
aws configure --profile staging

# Update staging parameters file
# Edit: cloudformation/parameters/staging.json

# Deploy staging infrastructure
.\deploy-staging.ps1 -AwsProfile staging
```

### 5. Production Environment

For production deployment (requires extra confirmation):

```powershell
# Configure AWS credentials for production
aws configure --profile production

# Update production parameters file with real values
# Edit: cloudformation/parameters/production.json

# Deploy production infrastructure (with safety prompts)
.\deploy-production.ps1 -AwsProfile production
```

## Configuration Files

### Parameters Files

Each environment has its own parameters file in `cloudformation/parameters/`:

- **development.json**: LocalStack-specific settings, minimal resources
- **qa.json**: Real AWS settings for testing, moderate resources
- **staging.json**: Production-like settings, enhanced security
- **production.json**: Full production settings, maximum security

### Environment Variables

Each environment has its own `.env` file:

- **.env.development**: LocalStack endpoints, debug enabled
- **.env.qa**: Real AWS endpoints, testing features enabled
- **.env.staging**: Production-like settings, limited debug
- **.env.production**: Production settings, minimal logging

## Deployment Scripts

### Universal Script

`deploy-infrastructure.ps1` - Automatically detects environment and uses appropriate template:

```powershell
# Development (LocalStack)
.\deploy-infrastructure.ps1 -Environment development

# QA (Real AWS)
.\deploy-infrastructure.ps1 -Environment qa -AwsProfile qa

# Staging (Real AWS)
.\deploy-infrastructure.ps1 -Environment staging -AwsProfile staging

# Production (Real AWS)
.\deploy-infrastructure.ps1 -Environment production -AwsProfile production
```

### Environment-Specific Scripts

- `deploy-qa.ps1` - QA deployment with validation
- `deploy-staging.ps1` - Staging deployment with confirmation
- `deploy-production.ps1` - Production deployment with multiple safety checks

## Prerequisites

### For Development (LocalStack)

1. Docker Desktop installed and running
2. LocalStack Pro license (for advanced features)
3. AWS CLI installed
4. PowerShell 5.1 or later

### For QA/Staging/Production (Real AWS)

1. AWS CLI installed and configured
2. Appropriate AWS credentials and permissions
3. SSL certificates for custom domains (ACM)
4. Route 53 hosted zones (if using custom domains)
5. PowerShell 5.1 or later

## Security Considerations

### Development
- Uses test credentials
- No encryption
- No access logging
- Minimal monitoring

### QA
- Real AWS credentials required
- Basic encryption enabled
- Access logging enabled
- Enhanced monitoring

### Staging
- Production-like security
- Full encryption
- WAF enabled
- Complete monitoring

### Production
- Maximum security settings
- Multi-AZ database
- Termination protection
- Full monitoring and alerting
- WAF and CloudFront enabled

## Monitoring and Alerting

### CloudWatch Metrics
- Development: Disabled
- QA: Basic metrics
- Staging: Detailed metrics
- Production: All metrics with custom dashboards

### Alerting
- Development: No alerts
- QA: Basic <NAME_EMAIL>
- Staging: Enhanced <NAME_EMAIL>
- Production: Full <NAME_EMAIL>

## Cost Optimization

### Development
- Uses LocalStack (free/minimal cost)
- Smallest instance sizes
- No redundancy

### QA
- Small instance sizes
- Single AZ deployment
- 7-day backup retention

### Staging
- Medium instance sizes
- Single AZ deployment
- 7-day backup retention

### Production
- Optimized instance sizes
- Multi-AZ deployment
- 30-day backup retention
- Reserved instances recommended

## Troubleshooting

### Common Issues

1. **LocalStack not accessible**
   ```powershell
   # Check if LocalStack is running
   docker ps | grep localstack
   
   # Restart LocalStack
   .\stop.ps1
   .\start.ps1
   ```

2. **AWS credentials not configured**
   ```powershell
   # Configure AWS profile
   aws configure --profile qa
   
   # Test credentials
   aws sts get-caller-identity --profile qa
   ```

3. **CloudFormation template validation errors**
   ```powershell
   # Validate template
   aws cloudformation validate-template --template-body file://cloudformation/gameflex-production-infrastructure.yaml
   ```

4. **Parameter file contains placeholders**
   - Edit the parameters file for your environment
   - Replace ACCOUNT_ID and CERTIFICATE_ID with real values
   - Update domain names and other environment-specific values

### Getting Help

Use the environment management script to diagnose issues:

```powershell
# Check all environments
.\manage-environments.ps1

# Validate specific environment
.\manage-environments.ps1 -Action validate -Environment qa
```

## Next Steps

1. **Set up CI/CD**: Integrate with GitHub Actions or Azure DevOps
2. **Database migrations**: Implement automated schema updates
3. **Blue-green deployments**: Set up zero-downtime deployments
4. **Disaster recovery**: Implement cross-region backups
5. **Performance testing**: Set up load testing for staging environment
