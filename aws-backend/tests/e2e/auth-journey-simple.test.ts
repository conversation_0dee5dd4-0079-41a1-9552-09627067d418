/**
 * End-to-End tests for complete Auth journey
 * Tests realistic user scenarios from signup to profile management
 */

import { AuthApiClient, expectSuccessResponse, expectErrorResponse, createTestUser, authenticateTestUser } from '../utils/api-helpers';
import { <PERSON><PERSON><PERSON><PERSON>elper, S3<PERSON>elper } from '../utils/aws-helpers';

describe('Auth Journey E2E Tests', () => {
  let authClient: AuthApiClient;
  let dynamoHelper: DynamoDBHelper;

  beforeAll(async () => {
    authClient = new AuthApiClient();
    dynamoHelper = new DynamoDBHelper();
  });

  beforeEach(async () => {
    // Clean up any test data from previous runs
    await dynamoHelper.cleanupTestUsers();
  });

  afterEach(async () => {
    // Clean up test data after each test
    await dynamoHelper.cleanupTestUsers();
  });

  describe('New User Journey', () => {
    it('should complete full new user onboarding flow', async () => {
      const testEmail = `newuser-${Date.now()}@example.com`;
      const testPassword = 'NewUserPassword123!';
      const testUsername = `newuser${Date.now()}`;

      // 1. User signs up
      const signupResponse = await authClient.signup(testEmail, testPassword, testUsername);
      expectSuccessResponse(signupResponse, 201);

      expect(signupResponse.body.message).toBe('User created successfully');
      expect(signupResponse.body.user.email).toBe(testEmail);
      expect(signupResponse.body.user.username).toBe(testUsername);

      // 2. User signs in for the first time
      const signinResponse = await authClient.signin(testEmail, testPassword);
      expectSuccessResponse(signinResponse, 200);

      expect(signinResponse.body.message).toBe('Authentication successful');
      expect(signinResponse.body.tokens.access_token).toBeDefined();
      expect(signinResponse.body.user.email).toBe(testEmail);

      const accessToken = signinResponse.body.tokens.access_token;

      // 3. User views their profile
      const profileResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(profileResponse, 200);

      expect(profileResponse.body.user.email).toBe(testEmail);
      expect(profileResponse.body.user.username).toBe(testUsername);
      expect(profileResponse.body.user.display_name).toBe(testUsername); // Default display name

      // 4. User updates their profile
      const updateResponse = await authClient.put('/auth/profile', {
        display_name: 'My Display Name',
        avatar_url: 'https://example.com/my-avatar.jpg'
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(updateResponse, 200);

      expect(updateResponse.body.message).toBe('Profile updated successfully');
      expect(updateResponse.body.user.display_name).toBe('My Display Name');
      expect(updateResponse.body.user.avatar_url).toBe('https://example.com/my-avatar.jpg');

      // 5. User signs out
      const signoutResponse = await authClient.signout(accessToken);
      expectSuccessResponse(signoutResponse, 200);

      expect(signoutResponse.body.message).toBe('Signed out successfully');

      // 6. Verify user can sign in again
      const secondSigninResponse = await authClient.signin(testEmail, testPassword);
      expectSuccessResponse(secondSigninResponse, 200);

      expect(secondSigninResponse.body.user.display_name).toBe('My Display Name');
    });

    it('should handle user who forgets password', async () => {
      const testEmail = `forgetful-${Date.now()}@example.com`;
      const testPassword = 'OriginalPassword123!';
      const testUsername = `forgetful${Date.now()}`;

      // 1. User signs up
      await authClient.signup(testEmail, testPassword, testUsername);

      // 2. User forgets password and requests reset
      const forgotResponse = await authClient.post('/auth/forgot-password', {
        email: testEmail
      });
      expectSuccessResponse(forgotResponse, 200);

      expect(forgotResponse.body.message).toBe('Password reset code sent to your email');

      // 3. User tries to reset with invalid code (simulating real scenario)
      const invalidResetResponse = await authClient.post('/auth/reset-password', {
        email: testEmail,
        confirmation_code: 'invalid-code',
        new_password: 'NewPassword123!'
      });
      expectErrorResponse(invalidResetResponse, 400, 'Invalid confirmation code');

      // Note: In a real scenario, user would get the code from email
      // For testing purposes, we verify the error handling works
    });
  });

  describe('Returning User Journey', () => {
    it('should handle returning user session management', async () => {
      // Use existing test user
      const authData = await authenticateTestUser();
      const accessToken = authData.accessToken;
      const refreshToken = authData.refreshToken;

      // 1. User accesses their profile
      const profileResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(profileResponse, 200);

      // 2. User updates their profile
      const updateResponse = await authClient.put('/auth/profile', {
        display_name: `Updated at ${new Date().toISOString()}`
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(updateResponse, 200);

      // 3. Token expires, user refreshes
      const refreshResponse = await authClient.refreshToken(refreshToken);
      expectSuccessResponse(refreshResponse, 200);

      expect(refreshResponse.body.tokens.access_token).toBeDefined();
      expect(refreshResponse.body.tokens.id_token).toBeDefined();

      // 4. User continues with new token
      const newAccessToken = refreshResponse.body.tokens.access_token;
      const profileWithNewTokenResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${newAccessToken}`
      });
      expectSuccessResponse(profileWithNewTokenResponse, 200);

      // 5. User signs out
      const signoutResponse = await authClient.signout(newAccessToken);
      expectSuccessResponse(signoutResponse, 200);
    });
  });

  describe('Security Scenarios', () => {
    it('should prevent unauthorized access', async () => {
      // 1. Try to access profile without token
      const noTokenResponse = await authClient.get('/auth/profile');
      expectErrorResponse(noTokenResponse, 401, 'Authorization header required');

      // 2. Try to access profile with invalid token
      const invalidTokenResponse = await authClient.get('/auth/profile', {
        Authorization: 'Bearer invalid-token'
      });
      expectErrorResponse(invalidTokenResponse, 401, 'Invalid or expired token');

      // 3. Try to update profile without authorization
      const unauthorizedUpdateResponse = await authClient.put('/auth/profile', {
        display_name: 'Hacker Name'
      });
      expectErrorResponse(unauthorizedUpdateResponse, 401, 'Authorization header required');
    });

    it('should handle malformed requests gracefully', async () => {
      // 1. Invalid JSON in signup
      const invalidJsonResponse = await authClient.post('/auth/signup', 'invalid-json');
      expectErrorResponse(invalidJsonResponse, 400);

      // 2. Missing required fields
      const missingFieldsResponse = await authClient.post('/auth/signin', {
        email: '<EMAIL>'
        // Missing password
      });
      expectErrorResponse(missingFieldsResponse, 400, 'Email and password are required');

      // 3. Invalid email format (handled by validation)
      const invalidEmailResponse = await authClient.post('/auth/signup', {
        email: 'not-an-email',
        password: 'Password123!',
        username: 'testuser'
      });
      // This might be handled by Cognito validation
      expect([400, 500]).toContain(invalidEmailResponse.statusCode);
    });
  });

  describe('Edge Cases', () => {
    it('should handle duplicate registration attempts', async () => {
      const testEmail = `duplicate-${Date.now()}@example.com`;
      const testPassword = 'DuplicatePassword123!';
      const testUsername = `duplicate${Date.now()}`;

      // 1. First registration succeeds
      const firstSignupResponse = await authClient.signup(testEmail, testPassword, testUsername);
      expectSuccessResponse(firstSignupResponse, 201);

      // 2. Second registration with same email fails
      const duplicateResponse = await authClient.signup(testEmail, testPassword, `${testUsername}2`);
      expectErrorResponse(duplicateResponse, 409, 'User already exists');
    });

    it('should handle concurrent requests', async () => {
      const authData = await authenticateTestUser();
      const accessToken = authData.accessToken;

      // Make multiple concurrent profile requests
      const promises = Array.from({ length: 5 }, () =>
        authClient.get('/auth/profile', {
          Authorization: `Bearer ${accessToken}`
        })
      );

      const responses = await Promise.all(promises);

      // All requests should succeed
      responses.forEach(response => {
        expectSuccessResponse(response, 200);
      });
    });

    it('should handle profile updates with edge case values', async () => {
      const authData = await authenticateTestUser();
      const accessToken = authData.accessToken;

      // Test with empty string
      const emptyStringResponse = await authClient.put('/auth/profile', {
        display_name: ''
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(emptyStringResponse, 200);

      // Test with very long string
      const longString = 'A'.repeat(1000);
      const longStringResponse = await authClient.put('/auth/profile', {
        display_name: longString
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      // Should either succeed or fail gracefully
      expect([200, 400]).toContain(longStringResponse.statusCode);

      // Test with null value (should be ignored)
      const nullValueResponse = await authClient.put('/auth/profile', {
        display_name: null
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(nullValueResponse, 200);
    });
  });
});

describe('Authentication Flow with Deployed Endpoints', () => {
  it('should handle complete signin validation flow', async () => {
    // Test the complete validation flow for signin
    const testCases = [
      {
        description: 'missing email and password',
        input: {},
        expectedStatus: 400,
        expectedError: 'Email and password are required'
      },
      {
        description: 'missing email',
        input: { password: 'password123' },
        expectedStatus: 400,
        expectedError: 'Email and password are required'
      },
      {
        description: 'missing password',
        input: { email: '<EMAIL>' },
        expectedStatus: 400,
        expectedError: 'Email and password are required'
      },
      {
        description: 'invalid credentials',
        input: { email: '<EMAIL>', password: 'wrongpassword' },
        expectedStatus: 401,
        expectedError: 'Invalid email or password'
      },
      {
        description: 'malformed email',
        input: { email: 'invalid-email', password: 'password123' },
        expectedStatus: 401,
        expectedError: 'Invalid email or password'
      }
    ];

    for (const testCase of testCases) {
      const response = await authClient.post('/auth/signin', testCase.input);

      expect(response.statusCode).toBe(testCase.expectedStatus);
      expect(response.body).toBeDefined();
      expect(response.body.error).toContain(testCase.expectedError);
    }
  });

  it('should handle various input formats and edge cases', async () => {
    // Test with different input formats
    const edgeCases = [
      {
        description: 'empty strings',
        input: { email: '', password: '' },
        expectedStatus: 400
      },
      {
        description: 'null values',
        input: { email: null, password: null },
        expectedStatus: 400
      },
      {
        description: 'very long email',
        input: {
          email: 'a'.repeat(100) + '@example.com',
          password: 'password123'
        },
        expectedStatus: 401
      },
      {
        description: 'very long password',
        input: {
          email: '<EMAIL>',
          password: 'a'.repeat(1000)
        },
        expectedStatus: 401
      }
    ];

    for (const testCase of edgeCases) {
      const response = await authClient.post('/auth/signin', testCase.input);
      expect(response.statusCode).toBeGreaterThanOrEqual(400);
      expect(response.body).toBeDefined();
    }
  });

  it('should handle malformed JSON requests', async () => {
    const malformedRequests = [
      'invalid json',
      '{"email": "<EMAIL>", "password":}', // Invalid JSON
      '{"email": "<EMAIL>"', // Incomplete JSON
      '', // Empty string
      '{', // Incomplete object
    ];

    for (const malformedJson of malformedRequests) {
      const response = await authClient.post('/auth/signin', malformedJson, {
        'Content-Type': 'application/json'
      });

      expect(response.statusCode).toBe(400);
      expect(response.body.error).toBe('Invalid JSON in request body');
    }
  });
});

describe('API Gateway Integration', () => {
  it('should handle different HTTP methods correctly', async () => {
    // Test that only POST is allowed for signin
    const methods = [
      { method: 'GET', expectedStatus: 403 },
      { method: 'PUT', expectedStatus: 403 },
      { method: 'DELETE', expectedStatus: 403 },
      { method: 'PATCH', expectedStatus: 403 },
      { method: 'OPTIONS', expectedStatus: 403 }
    ];

    for (const { method, expectedStatus } of methods) {
      let response;
      switch (method) {
        case 'GET':
          response = await authClient.get('/auth/signin');
          break;
        case 'PUT':
          response = await authClient.put('/auth/signin', {});
          break;
        case 'DELETE':
          response = await authClient.delete('/auth/signin');
          break;
        case 'OPTIONS':
          response = await authClient.options('/auth/signin');
          break;
        default:
          continue;
      }

      expect(response.statusCode).toBe(expectedStatus);
    }
  });

  it('should handle different content types', async () => {
    const contentTypes = [
      'application/json',
      'text/plain',
      'application/x-www-form-urlencoded'
    ];

    for (const contentType of contentTypes) {
      const response = await authClient.post('/auth/signin',
        JSON.stringify({ email: '<EMAIL>', password: 'password123' }),
        { 'Content-Type': contentType }
      );

      // Should handle all content types gracefully
      expect(response.statusCode).toBeGreaterThanOrEqual(400);
      expect(response.body).toBeDefined();
    }
  });
});

describe('Lambda Function Behavior', () => {
  it('should maintain consistent response format', async () => {
    const response = await authClient.signin('<EMAIL>', 'wrongpassword');

    // Verify response structure
    expect(response.statusCode).toBe(401);
    expect(response.body).toBeDefined();
    expect(typeof response.body).toBe('object');
    expect(response.body.error).toBeDefined();
    expect(typeof response.body.error).toBe('string');
  });

  it('should handle rapid successive requests', async () => {
    const requests: Promise<any>[] = [];
    for (let i = 0; i < 5; i++) {
      requests.push(
        authClient.signin(`test${i}@example.com`, 'password123')
      );
    }

    const responses = await Promise.all(requests);

    responses.forEach((response, index) => {
      // All should return consistent error responses
      expect([401, 502]).toContain(response.statusCode); // 502 for LocalStack timeouts
      if (response.statusCode === 401) {
        expect(response.body.error).toBe('Invalid email or password');
      }
    });
  });
});

describe('Infrastructure Integration', () => {
  it('should verify AWS services are accessible', async () => {
    // Test that the Lambda can access DynamoDB (indirectly)
    // Since we can't create users, we test that the Lambda doesn't crash
    const response = await authClient.signin('<EMAIL>', 'password123');

    // Should get a proper authentication error, not a service error
    expect(response.statusCode).toBe(401);
    expect(response.body.error).toBe('Invalid email or password');

    // This confirms the Lambda is running and can process requests
    // without throwing internal service errors
  });

  it('should handle timeout scenarios gracefully', async () => {
    // Test with a request that might take longer
    const response = await authClient.post('/auth/signin', {
      email: '<EMAIL>',
      password: 'password123',
      // Add some extra data that might slow processing
      metadata: 'x'.repeat(10000)
    });

    // Should still process the request
    expect([401, 502]).toContain(response.statusCode);
  });
});

describe('Error Handling and Resilience', () => {
  it('should handle various error conditions gracefully', async () => {
    const errorConditions = [
      // Test various problematic inputs
      { email: '<EMAIL>', password: null },
      { email: undefined, password: 'password123' },
      { email: 123, password: 'password123' }, // Wrong type
      { email: '<EMAIL>', password: 456 }, // Wrong type
    ];

    for (const condition of errorConditions) {
      const response = await authClient.post('/auth/signin', condition);

      // Should handle gracefully without crashing
      expect(response.statusCode).toBeGreaterThanOrEqual(400);
      expect(response.body).toBeDefined();
    }
  });

  it('should maintain service availability under load', async () => {
    // Test that the service remains responsive
    const startTime = Date.now();
    const response = await authClient.signin('<EMAIL>', 'password123');
    const endTime = Date.now();

    // Should respond within reasonable time (10 seconds for LocalStack)
    expect(endTime - startTime).toBeLessThan(10000);
    expect(response.statusCode).toBe(401);
  });
});
});
