/**
 * Environment setup for tests
 * Sets up LocalStack environment variables and configuration
 */

// LocalStack configuration
process.env.AWS_ENDPOINT_URL = 'http://localhost:45660';
process.env.AWS_DEFAULT_REGION = 'us-east-1';
process.env.AWS_ACCESS_KEY_ID = 'test';
process.env.AWS_SECRET_ACCESS_KEY = 'test';
process.env.AWS_SESSION_TOKEN = 'test';

// Cognito configuration for tests
process.env.COGNITO_USER_POOL_ID = 'us-east-1_c913f87bd57b42ec8774e85ca54855cd';
process.env.COGNITO_USER_POOL_CLIENT_ID = 'cy9pj1t6913kgxpox49pvid3ne';

// DynamoDB table names
process.env.USERS_TABLE = 'UserProfiles';
process.env.POSTS_TABLE = 'Posts';
process.env.MEDIA_TABLE = 'Media';

// S3 bucket names
process.env.MEDIA_BUCKET = 'gameflex-media-development';
process.env.AVATARS_BUCKET = 'gameflex-avatars-development';
process.env.TEMP_BUCKET = 'gameflex-temp-development';

// API Gateway configuration - will be set dynamically
// process.env.API_GATEWAY_URL will be set by getApiGatewayUrl() function

// Test environment flag
process.env.NODE_ENV = 'test';
process.env.ENVIRONMENT = 'development';

// Disable AWS SDK retries for faster tests
process.env.AWS_MAX_ATTEMPTS = '1';

// Enable debug logging for tests
process.env.DEBUG = 'gameflex:*';

export { };
