/**
 * Integration tests for Auth functionality
 * Tests with real LocalStack Cognito and DynamoDB services
 */

import { CognitoIdentityProviderClient, AdminCreateUserCommand, AdminDeleteUserCommand, ListUsersCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient, ScanCommand, DeleteItemCommand } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { AuthApiClient, expectSuccessResponse, expectErrorResponse } from '../utils/api-helpers';

// LocalStack configuration
const cognitoClient = new CognitoIdentityProviderClient({
  endpoint: 'http://localhost:45660',
  region: 'us-east-1',
  credentials: {
    accessKeyId: 'test',
    secretAccessKey: 'test'
  }
});

const dynamoClient = new DynamoDBClient({
  endpoint: 'http://localhost:45660',
  region: 'us-east-1',
  credentials: {
    accessKeyId: 'test',
    secretAccessKey: 'test'
  }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

describe('Auth Integration Tests', () => {
  let authClient: AuthApiClient;
  const testUserEmail = `test-${Date.now()}@example.com`;
  const testUsername = `testuser${Date.now()}`;
  const testPassword = 'TestPassword123!';
  let userPoolId: string;
  let usersTableName: string;

  beforeAll(async () => {
    authClient = new AuthApiClient();
    
    // Get environment configuration
    userPoolId = process.env.COGNITO_USER_POOL_ID || 'us-east-1_TestPool';
    usersTableName = process.env.DYNAMODB_TABLE_USERS || 'gameflex-development-Users';
  });

  afterEach(async () => {
    // Clean up test users from Cognito
    try {
      const listUsersResponse = await cognitoClient.send(new ListUsersCommand({
        UserPoolId: userPoolId,
        Filter: `email = "${testUserEmail}"`
      }));

      for (const user of listUsersResponse.Users || []) {
        if (user.Username) {
          await cognitoClient.send(new AdminDeleteUserCommand({
            UserPoolId: userPoolId,
            Username: user.Username
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to clean up Cognito users:', error);
    }

    // Clean up test users from DynamoDB
    try {
      const scanResponse = await dynamoClient.send(new ScanCommand({
        TableName: usersTableName,
        FilterExpression: 'email = :email',
        ExpressionAttributeValues: {
          ':email': { S: testUserEmail }
        }
      }));

      for (const item of scanResponse.Items || []) {
        if (item.id?.S) {
          await dynamoClient.send(new DeleteItemCommand({
            TableName: usersTableName,
            Key: {
              id: { S: item.id.S }
            }
          }));
        }
      }
    } catch (error) {
      console.warn('Failed to clean up DynamoDB users:', error);
    }
  });

  describe('Complete Auth Flow', () => {
    it('should complete full signup -> signin -> profile -> signout flow', async () => {
      // 1. Sign up
      const signupResponse = await authClient.signup(testUserEmail, testPassword, testUsername);
      expectSuccessResponse(signupResponse, 201);
      
      const signupBody = signupResponse.body;
      expect(signupBody.message).toBe('User created successfully');
      expect(signupBody.user.email).toBe(testUserEmail);
      expect(signupBody.user.username).toBe(testUsername);

      // 2. Sign in
      const signinResponse = await authClient.signin(testUserEmail, testPassword);
      expectSuccessResponse(signinResponse, 200);
      
      const signinBody = signinResponse.body;
      expect(signinBody.message).toBe('Authentication successful');
      expect(signinBody.tokens.access_token).toBeDefined();
      expect(signinBody.tokens.refresh_token).toBeDefined();
      expect(signinBody.tokens.id_token).toBeDefined();
      expect(signinBody.user.email).toBe(testUserEmail);

      const accessToken = signinBody.tokens.access_token;

      // 3. Get profile
      const profileResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(profileResponse, 200);
      
      const profileBody = profileResponse.body;
      expect(profileBody.user.email).toBe(testUserEmail);
      expect(profileBody.user.username).toBe(testUsername);

      // 4. Update profile
      const updateResponse = await authClient.put('/auth/profile', {
        display_name: 'Updated Test User',
        avatar_url: 'https://example.com/avatar.jpg'
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectSuccessResponse(updateResponse, 200);
      
      const updateBody = updateResponse.body;
      expect(updateBody.message).toBe('Profile updated successfully');
      expect(updateBody.user.display_name).toBe('Updated Test User');
      expect(updateBody.user.avatar_url).toBe('https://example.com/avatar.jpg');

      // 5. Sign out
      const signoutResponse = await authClient.signout(accessToken);
      expectSuccessResponse(signoutResponse, 200);
      
      const signoutBody = signoutResponse.body;
      expect(signoutBody.message).toBe('Signed out successfully');

      // 6. Verify token is invalid after signout
      const profileAfterSignoutResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${accessToken}`
      });
      expectErrorResponse(profileAfterSignoutResponse, 401);
    });

    it('should handle token refresh flow', async () => {
      // Sign up and sign in first
      await authClient.signup(testUserEmail, testPassword, testUsername);
      const signinResponse = await authClient.signin(testUserEmail, testPassword);
      
      const refreshToken = signinResponse.body.tokens.refresh_token;

      // Refresh tokens
      const refreshResponse = await authClient.refreshToken(refreshToken);
      expectSuccessResponse(refreshResponse, 200);
      
      const refreshBody = refreshResponse.body;
      expect(refreshBody.message).toBe('Token refreshed successfully');
      expect(refreshBody.tokens.access_token).toBeDefined();
      expect(refreshBody.tokens.id_token).toBeDefined();

      // Verify new access token works
      const newAccessToken = refreshBody.tokens.access_token;
      const profileResponse = await authClient.get('/auth/profile', {
        Authorization: `Bearer ${newAccessToken}`
      });
      expectSuccessResponse(profileResponse, 200);
    });

    it('should handle password reset flow', async () => {
      // Sign up first
      await authClient.signup(testUserEmail, testPassword, testUsername);

      // Request password reset
      const forgotResponse = await authClient.post('/auth/forgot-password', {
        email: testUserEmail
      });
      expectSuccessResponse(forgotResponse, 200);
      
      const forgotBody = forgotResponse.body;
      expect(forgotBody.message).toBe('Password reset code sent to your email');

      // Note: In a real test, you would get the confirmation code from email
      // For LocalStack testing, we'll test the error case with invalid code
      const resetResponse = await authClient.post('/auth/reset-password', {
        email: testUserEmail,
        confirmation_code: 'invalid-code',
        new_password: 'NewPassword123!'
      });
      expectErrorResponse(resetResponse, 400, 'Invalid confirmation code');
    });
  });

  describe('Error Handling', () => {
    it('should prevent duplicate user registration', async () => {
      // Create user first
      await authClient.signup(testUserEmail, testPassword, testUsername);

      // Try to create same user again
      const duplicateResponse = await authClient.signup(testUserEmail, testPassword, testUsername);
      expectErrorResponse(duplicateResponse, 409, 'User already exists');
    });

    it('should reject invalid credentials', async () => {
      // Try to sign in with non-existent user
      const invalidResponse = await authClient.signin('<EMAIL>', 'wrongpassword');
      expectErrorResponse(invalidResponse, 401, 'Invalid email or password');
    });

    it('should reject invalid refresh tokens', async () => {
      const invalidRefreshResponse = await authClient.refreshToken('invalid-refresh-token');
      expectErrorResponse(invalidRefreshResponse, 401, 'Invalid refresh token');
    });

    it('should reject requests without authorization', async () => {
      const unauthorizedResponse = await authClient.get('/auth/profile');
      expectErrorResponse(unauthorizedResponse, 401, 'Authorization header required');
    });

    it('should reject invalid authorization tokens', async () => {
      const invalidTokenResponse = await authClient.get('/auth/profile', {
        Authorization: 'Bearer invalid-token'
      });
      expectErrorResponse(invalidTokenResponse, 401, 'Invalid or expired token');
    });
  });

  describe('Input Validation', () => {
    it('should validate signup input', async () => {
      // Missing email
      const missingEmailResponse = await authClient.post('/auth/signup', {
        password: testPassword,
        username: testUsername
      });
      expectErrorResponse(missingEmailResponse, 400, 'Email, password, and username are required');

      // Missing password
      const missingPasswordResponse = await authClient.post('/auth/signup', {
        email: testUserEmail,
        username: testUsername
      });
      expectErrorResponse(missingPasswordResponse, 400, 'Email, password, and username are required');

      // Missing username
      const missingUsernameResponse = await authClient.post('/auth/signup', {
        email: testUserEmail,
        password: testPassword
      });
      expectErrorResponse(missingUsernameResponse, 400, 'Email, password, and username are required');
    });

    it('should validate signin input', async () => {
      // Missing email
      const missingEmailResponse = await authClient.post('/auth/signin', {
        password: testPassword
      });
      expectErrorResponse(missingEmailResponse, 400, 'Email and password are required');

      // Missing password
      const missingPasswordResponse = await authClient.post('/auth/signin', {
        email: testUserEmail
      });
      expectErrorResponse(missingPasswordResponse, 400, 'Email and password are required');
    });

    it('should validate profile update input', async () => {
      // Sign up and sign in first
      await authClient.signup(testUserEmail, testPassword, testUsername);
      const signinResponse = await authClient.signin(testUserEmail, testPassword);
      const accessToken = signinResponse.body.tokens.access_token;

      // Try to update with no valid fields
      const noFieldsResponse = await authClient.put('/auth/profile', {
        invalid_field: 'some value'
      }, {
        Authorization: `Bearer ${accessToken}`
      });
      expectErrorResponse(noFieldsResponse, 400, 'No valid fields to update');
    });
  });
});
