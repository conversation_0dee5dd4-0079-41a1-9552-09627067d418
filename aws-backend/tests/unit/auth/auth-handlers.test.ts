/**
 * Comprehensive unit tests for Auth Lambda handlers
 * Tests all authentication functionality with mocked AWS services
 */

import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { mockClient } from 'aws-sdk-client-mock';
import { CognitoIdentityProviderClient, AdminCreateUserCommand, AdminSetUserPasswordCommand, AdminInitiateAuthCommand, GetUserCommand, GlobalSignOutCommand, ForgotPasswordCommand, ConfirmForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBDocumentClient, PutCommand, QueryCommand, GetCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';

// Mock AWS clients BEFORE importing the handler
const cognitoMock = mockClient(CognitoIdentityProviderClient);
const dynamoMock = mockClient(DynamoDBDocumentClient);

// Import handler AFTER setting up mocks
import { handler } from '../../../lambda-functions/auth/src/handler';

// Mock context
const mockContext: Context = {
  callbackWaitsForEmptyEventLoop: false,
  functionName: 'test-function',
  functionVersion: '1',
  invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:test-function',
  memoryLimitInMB: '128',
  awsRequestId: 'test-request-id',
  logGroupName: '/aws/lambda/test-function',
  logStreamName: '2023/01/01/[$LATEST]test-stream',
  getRemainingTimeInMillis: () => 30000,
  done: () => { },
  fail: () => { },
  succeed: () => { }
};

// Helper function to create API Gateway event
function createEvent(
  httpMethod: string,
  path: string,
  body?: any,
  headers?: Record<string, string>
): APIGatewayProxyEvent {
  return {
    httpMethod,
    path,
    body: body ? JSON.stringify(body) : null,
    headers: headers || {},
    multiValueHeaders: {},
    queryStringParameters: null,
    multiValueQueryStringParameters: null,
    pathParameters: null,
    stageVariables: null,
    requestContext: {
      accountId: '************',
      apiId: 'test-api',
      protocol: 'HTTP/1.1',
      httpMethod,
      path,
      stage: 'test',
      requestId: 'test-request',
      requestTime: '01/Jan/2023:00:00:00 +0000',
      requestTimeEpoch: *************,
      resourceId: 'test-resource',
      resourcePath: path,
      identity: {
        accessKey: null,
        accountId: null,
        apiKey: null,
        apiKeyId: null,
        caller: null,
        cognitoAuthenticationProvider: null,
        cognitoAuthenticationType: null,
        cognitoIdentityId: null,
        cognitoIdentityPoolId: null,
        principalOrgId: null,
        sourceIp: '127.0.0.1',
        user: null,
        userAgent: 'test-agent',
        userArn: null,
        clientCert: null
      },
      authorizer: null
    },
    resource: path,
    isBase64Encoded: false
  };
}

describe('Auth Lambda Handler', () => {
  beforeEach(() => {
    // Reset all mocks
    cognitoMock.reset();
    dynamoMock.reset();

    // Set environment variables
    process.env.COGNITO_USER_POOL_ID = 'us-east-1_TestPool';
    process.env.COGNITO_USER_POOL_CLIENT_ID = 'test-client-id';
    process.env.DYNAMODB_TABLE_USERS = 'test-users-table';
  });

  describe('CORS handling', () => {
    it('should handle OPTIONS requests', async () => {
      const event = createEvent('OPTIONS', '/auth/signin');
      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);
      expect(result.headers?.['Access-Control-Allow-Origin']).toBe('*');
      expect(result.headers?.['Access-Control-Allow-Methods']).toContain('POST');
    });
  });

  describe('POST /auth/signup', () => {
    it('should create a new user successfully', async () => {
      // Use unique email for each test run to avoid conflicts
      const uniqueEmail = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `testuser${Date.now()}`;

      const event = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User created successfully');
      expect(body.user.email).toBe(uniqueEmail);
      expect(body.user.username).toBe(uniqueUsername);
    });

    it('should return error for missing fields', async () => {
      const event = createEvent('POST', '/auth/signup', {
        email: '<EMAIL>'
        // Missing password and username
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email, password, and username are required');
    });

    it('should handle user already exists error', async () => {
      // Use an email that we know exists in the test data
      const event = createEvent('POST', '/auth/signup', {
        email: '<EMAIL>', // This user exists in LocalStack
        password: 'TestPassword123!',
        username: 'existinguser'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('User already exists');
    });
  });

  describe('POST /auth/signin', () => {
    it('should sign in user successfully', async () => {
      // Use the dev user that exists in the system
      const event = createEvent('POST', '/auth/signin', {
        email: '<EMAIL>',
        password: 'DevPassword123!'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Authentication successful');
      expect(body.tokens.access_token).toBeDefined();
      expect(body.tokens.refresh_token).toBeDefined();
      expect(body.tokens.id_token).toBeDefined();
      expect(body.user.email).toBe('<EMAIL>');
    });

    it('should return error for invalid credentials', async () => {
      const event = createEvent('POST', '/auth/signin', {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid email or password');
    });

    it('should return error for missing fields', async () => {
      const event = createEvent('POST', '/auth/signin', {
        email: '<EMAIL>'
        // Missing password
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email and password are required');
    });
  });

  describe('POST /auth/refresh', () => {
    it('should refresh tokens successfully with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `refresh-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `refreshuser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const refreshToken = signinBody.tokens.refresh_token;

      // Now test refresh with real token
      const refreshEvent = createEvent('POST', '/auth/refresh', {
        refresh_token: refreshToken
      });

      const result = await handler(refreshEvent, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Token refreshed successfully');
      expect(body.tokens.access_token).toBeDefined();
      expect(body.tokens.id_token).toBeDefined();
    });

    it('should return error for missing refresh token', async () => {
      const event = createEvent('POST', '/auth/refresh', {});

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Refresh token is required');
    });

    it('should return error for invalid refresh token', async () => {
      const error = new Error('Invalid refresh token');
      error.name = 'NotAuthorizedException';
      cognitoMock.on(AdminInitiateAuthCommand).rejects(error);

      const event = createEvent('POST', '/auth/refresh', {
        refresh_token: 'invalid-refresh-token'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid refresh token');
    });
  });

  describe('POST /auth/signout', () => {
    it('should sign out user successfully with real auth flow', async () => {
      // First create a new user
      const uniqueEmail = `signout-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`;
      const uniqueUsername = `signoutuser${Date.now()}`;

      const signupEvent = createEvent('POST', '/auth/signup', {
        email: uniqueEmail,
        password: 'TestPassword123!',
        username: uniqueUsername
      });

      const signupResult = await handler(signupEvent, mockContext);
      expect(signupResult.statusCode).toBe(201);

      // Then sign in to get real tokens
      const signinEvent = createEvent('POST', '/auth/signin', {
        email: uniqueEmail,
        password: 'TestPassword123!'
      });

      const signinResult = await handler(signinEvent, mockContext);
      expect(signinResult.statusCode).toBe(200);

      const signinBody = JSON.parse(signinResult.body);
      const accessToken = signinBody.tokens.access_token;

      // Now test signout with real token
      const signoutEvent = createEvent('POST', '/auth/signout', {}, {
        Authorization: `Bearer ${accessToken}`
      });

      const result = await handler(signoutEvent, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Signed out successfully');
    });

    it('should return error for missing authorization header', async () => {
      const event = createEvent('POST', '/auth/signout', {});

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(401);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Authorization header required');
    });
  });

  describe('POST /auth/forgot-password', () => {
    it('should send password reset code successfully', async () => {
      cognitoMock.on(ForgotPasswordCommand).resolves({});

      const event = createEvent('POST', '/auth/forgot-password', {
        email: '<EMAIL>'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Password reset code sent to your email');
    });

    it('should return error for missing email', async () => {
      const event = createEvent('POST', '/auth/forgot-password', {});

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email is required');
    });

    it('should not reveal if user does not exist', async () => {
      const error = new Error('User not found');
      error.name = 'UserNotFoundException';
      cognitoMock.on(ForgotPasswordCommand).rejects(error);

      const event = createEvent('POST', '/auth/forgot-password', {
        email: '<EMAIL>'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Password reset code sent to your email');
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should return error for invalid confirmation code (LocalStack limitation)', async () => {
      // Note: LocalStack has limitations with confirmation codes, so we test error handling
      const event = createEvent('POST', '/auth/reset-password', {
        email: '<EMAIL>',
        confirmation_code: '123456', // This will be invalid in LocalStack
        new_password: 'NewPassword123!'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid confirmation code');
    });

    it('should return error for missing fields', async () => {
      const event = createEvent('POST', '/auth/reset-password', {
        email: '<EMAIL>'
        // Missing confirmation_code and new_password
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Email, confirmation code, and new password are required');
    });

    it('should return error for invalid confirmation code', async () => {
      const error = new Error('Invalid code');
      error.name = 'CodeMismatchException';
      cognitoMock.on(ConfirmForgotPasswordCommand).rejects(error);

      const event = createEvent('POST', '/auth/reset-password', {
        email: '<EMAIL>',
        confirmation_code: 'invalid',
        new_password: 'NewPassword123!'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid confirmation code');
    });

    it('should return error for another invalid confirmation code', async () => {
      // Note: LocalStack treats all invalid codes the same way
      const event = createEvent('POST', '/auth/reset-password', {
        email: '<EMAIL>',
        confirmation_code: '999999', // Another invalid code
        new_password: 'NewPassword123!'
      });

      const result = await handler(event, mockContext);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid confirmation code');
    });
  });
});
