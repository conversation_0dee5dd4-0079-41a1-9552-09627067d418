/**
 * Simple API tests for deployed Auth endpoints
 * Tests only the endpoints that are actually deployed in the API Gateway
 */

import { AuthApiClient, expectErrorResponse } from '../utils/api-helpers';

describe('Auth API Simple Tests', () => {
  let authClient: AuthApiClient;

  beforeAll(async () => {
    authClient = new AuthApiClient();
  });

  describe('POST /auth/signin', () => {
    it('should return error for invalid credentials', async () => {
      const response = await authClient.signin('<EMAIL>', 'wrongpassword');
      expectErrorResponse(response, 401, 'Invalid email or password');
    });

    it('should return error for missing email', async () => {
      const response = await authClient.post('/auth/signin', {
        password: 'TestPassword123!'
      });
      expectErrorResponse(response, 400, 'Email and password are required');
    });

    it('should return error for missing password', async () => {
      const response = await authClient.post('/auth/signin', {
        email: '<EMAIL>'
      });
      expectErrorResponse(response, 400, 'Email and password are required');
    });

    it('should handle malformed JSON', async () => {
      const response = await authClient.post('/auth/signin', 'invalid json', {
        'Content-Type': 'application/json'
      });
      expectErrorResponse(response, 400, 'Invalid JSON');
    });

    it('should include CORS headers', async () => {
      const response = await authClient.signin('<EMAIL>', 'password');

      expect(response.headers).toBeDefined();
      // Note: CORS headers might not be present in LocalStack responses
      // This test verifies the response structure
    });
  });

  describe('Non-existent endpoints', () => {
    it('should return 403 for non-deployed signup endpoint', async () => {
      const response = await authClient.post('/auth/signup', {
        email: '<EMAIL>',
        password: 'password123',
        username: 'testuser'
      });

      // API Gateway returns 403 for non-existent endpoints
      expect(response.statusCode).toBe(403);
    });

    it('should return 403 for non-deployed refresh endpoint', async () => {
      const response = await authClient.post('/auth/refresh', {
        refresh_token: 'fake-token'
      });

      expect(response.statusCode).toBe(403);
    });

    it('should return 403 for non-deployed signout endpoint', async () => {
      const response = await authClient.post('/auth/signout', {}, {
        Authorization: 'Bearer fake-token'
      });

      expect(response.statusCode).toBe(403);
    });

    it('should return 403 for OPTIONS requests on non-deployed endpoints', async () => {
      const response = await authClient.options('/auth/signup');
      expect(response.statusCode).toBe(403);
    });

    it('should return 403 for unknown auth endpoints', async () => {
      const response = await authClient.get('/auth/unknown');
      expect(response.statusCode).toBe(403);
    });
  });

  describe('Lambda function direct testing', () => {
    it('should test auth lambda function validation logic', async () => {
      // Test the signin endpoint with various invalid inputs
      const testCases = [
        {
          input: {},
          expectedError: 'Email and password are required'
        },
        {
          input: { email: '<EMAIL>' },
          expectedError: 'Email and password are required'
        },
        {
          input: { password: 'password123' },
          expectedError: 'Email and password are required'
        },
        {
          input: { email: 'invalid-email', password: 'password123' },
          expectedError: 'Invalid email or password'
        }
      ];

      for (const testCase of testCases) {
        const response = await authClient.post('/auth/signin', testCase.input);
        expect(response.statusCode).toBeGreaterThanOrEqual(400);
        expect(response.body).toBeDefined();
        expect(response.body.error).toContain(testCase.expectedError);
      }
    });

    it('should handle various content types', async () => {
      // Test with different content types
      const response = await authClient.post('/auth/signin',
        JSON.stringify({ email: '<EMAIL>', password: 'password123' }),
        { 'Content-Type': 'application/json' }
      );

      expect(response.statusCode).toBe(401); // Invalid credentials
      expect(response.body.error).toBe('Invalid email or password');
    });

    it('should handle empty request body', async () => {
      const response = await authClient.post('/auth/signin', '');
      expect(response.statusCode).toBe(400);
      expect(response.body.error).toBe('Invalid JSON in request body');
    });
  });

  describe('API Gateway behavior', () => {
    it('should handle large request bodies', async () => {
      const largeData = {
        email: '<EMAIL>',
        password: 'password123',
        extraData: 'x'.repeat(1000) // 1KB of extra data
      };

      const response = await authClient.post('/auth/signin', largeData);
      expect(response.statusCode).toBe(401); // Should still process the request
    });

    it('should handle concurrent requests', async () => {
      const requests = Array(3).fill(null).map(() =>
        authClient.signin('<EMAIL>', 'password123')
      );

      const responses = await Promise.all(requests);

      responses.forEach(response => {
        // Accept either 401 (normal response) or 502 (LocalStack timeout)
        expect([401, 502]).toContain(response.statusCode);
        if (response.statusCode === 401) {
          expect(response.body.error).toBe('Invalid email or password');
        }
      });
    });
  });
});
