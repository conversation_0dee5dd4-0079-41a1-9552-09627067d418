/**
 * API testing helpers
 * Utilities for making HTTP requests to the API Gateway endpoints
 */

import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Cache for API Gateway URL to avoid repeated AWS calls
let cachedApiGatewayUrl: string | null = null;

async function getApiGatewayUrl(): Promise<string> {
  if (cachedApiGatewayUrl) {
    return cachedApiGatewayUrl;
  }

  try {
    // Get all REST APIs
    const { stdout } = await execAsync('awslocal apigateway get-rest-apis');
    const apis = JSON.parse(stdout);

    if (!apis.items || apis.items.length === 0) {
      throw new Error('No API Gateway REST APIs found');
    }

    // Find the GameFlex API (or use the first one if only one exists)
    let targetApi = apis.items.find((api: any) =>
      api.name && api.name.toLowerCase().includes('gameflex')
    );

    if (!targetApi && apis.items.length === 1) {
      targetApi = apis.items[0];
    }

    if (!targetApi) {
      throw new Error('GameFlex API Gateway not found');
    }

    const apiId = targetApi.id;

    // Get stages for this API
    const { stdout: stagesOutput } = await execAsync(`awslocal apigateway get-stages --rest-api-id ${apiId}`);
    const stages = JSON.parse(stagesOutput);

    if (!stages.item || stages.item.length === 0) {
      throw new Error(`No stages found for API ${apiId}`);
    }

    // Use development stage if available, otherwise use the first stage
    let targetStage = stages.item.find((stage: any) => stage.stageName === 'development');
    if (!targetStage) {
      targetStage = stages.item[0];
    }

    const stageName = targetStage.stageName;
    const baseUrl = `http://localhost:45660/restapis/${apiId}/${stageName}/_user_request_`;

    cachedApiGatewayUrl = baseUrl;
    console.log(`🔗 Using API Gateway URL: ${baseUrl}`);

    return baseUrl;
  } catch (error) {
    console.error('Failed to get API Gateway URL:', error);
    // Fallback to environment variable or default
    const fallbackUrl = process.env.API_GATEWAY_URL || 'http://localhost:45660/restapis/gmikyj5ogu/development/_user_request_';
    console.log(`⚠️  Using fallback API Gateway URL: ${fallbackUrl}`);
    cachedApiGatewayUrl = fallbackUrl;
    return fallbackUrl;
  }
}

// Get API Gateway URL synchronously (from environment variable set by global setup)
function getApiGatewayUrlSync(): string {
  const envUrl = process.env.API_GATEWAY_URL;
  if (envUrl) {
    console.log(`🔗 Using API Gateway URL from environment: ${envUrl}`);
    return envUrl;
  }

  // Fallback URL
  const fallbackUrl = 'http://localhost:45660/restapis/gmikyj5ogu/development/_user_request_';
  console.log(`⚠️  Using fallback API Gateway URL: ${fallbackUrl}`);
  return fallbackUrl;
}

// Initialize API_BASE_URL as a promise that resolves to the dynamic URL
const API_BASE_URL_PROMISE = getApiGatewayUrl();

// Export the sync function for immediate use
export { getApiGatewayUrlSync };

export interface ApiResponse<T = any> {
  statusCode: number;
  headers: Record<string, string>;
  body: T;
}

export class ApiClient {
  private baseURL: Promise<string>;
  private defaultHeaders: Record<string, string>;

  constructor(baseURL?: string, useSync: boolean = false) {
    if (baseURL) {
      this.baseURL = Promise.resolve(baseURL);
    } else if (useSync) {
      this.baseURL = Promise.resolve(getApiGatewayUrlSync());
    } else {
      this.baseURL = API_BASE_URL_PROMISE;
    }

    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };
  }

  private async makeRequest<T = any>(
    method: string,
    path: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    try {
      const baseURL = await this.baseURL;
      const fullUrl = `${baseURL}${path}`;

      // Log the API call details
      console.log(`🌐 API Request: ${method} ${fullUrl}`);
      if (data) {
        console.log(`📤 Request Body:`, JSON.stringify(data, null, 2));
      }
      if (headers && Object.keys(headers).length > 0) {
        console.log(`📋 Request Headers:`, headers);
      }

      const config: AxiosRequestConfig = {
        method,
        url: fullUrl,
        headers: { ...this.defaultHeaders, ...headers },
        data,
        validateStatus: () => true // Don't throw on HTTP error status codes
      };

      const response: AxiosResponse = await axios(config);

      // Log the response details
      console.log(`📥 API Response: ${response.status} ${response.statusText}`);
      if (response.data) {
        console.log(`📦 Response Body:`, JSON.stringify(response.data, null, 2));
      }

      return {
        statusCode: response.status,
        headers: response.headers as Record<string, string>,
        body: response.data
      };
    } catch (error: any) {
      // Handle network errors
      console.log(`❌ API Request Error: ${error.message}`);
      throw new Error(`API request failed: ${error.message}`);
    }
  }

  async get<T = any>(path: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('GET', path, undefined, headers);
  }

  async post<T = any>(path: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('POST', path, data, headers);
  }

  async put<T = any>(path: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('PUT', path, data, headers);
  }

  async delete<T = any>(path: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('DELETE', path, undefined, headers);
  }

  async options<T = any>(path: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.makeRequest<T>('OPTIONS', path, undefined, headers);
  }

  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }
}

export class AuthApiClient extends ApiClient {
  constructor() {
    console.log(`🏗️  Creating AuthApiClient with sync URL resolution`);
    super(undefined, true); // Use sync URL resolution
  }

  async signup(email: string, password: string, username: string) {
    console.log(`🔐 AuthApiClient.signup() - Calling signup endpoint`);
    return this.post('/auth/signup', { email, password, username });
  }

  async signin(email: string, password: string) {
    console.log(`🔐 AuthApiClient.signin() - Calling signin endpoint`);
    return this.post('/auth/signin', { email, password });
  }

  async refreshToken(refreshToken: string) {
    console.log(`🔐 AuthApiClient.refreshToken() - Calling refresh endpoint`);
    return this.post('/auth/refresh', { refresh_token: refreshToken });
  }

  async signout(accessToken: string) {
    console.log(`🔐 AuthApiClient.signout() - Calling signout endpoint`);
    return this.post('/auth/signout', {}, { Authorization: `Bearer ${accessToken}` });
  }
}

export class PostsApiClient extends ApiClient {
  constructor() {
    super(undefined, true); // Use sync URL resolution
  }

  async getPosts(accessToken?: string, limit?: number, offset?: number) {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    const query = params.toString() ? `?${params.toString()}` : '';
    const headers = accessToken ? { Authorization: `Bearer ${accessToken}` } : undefined;
    return this.get(`/posts${query}`, headers);
  }

  async createPost(accessToken: string, postData: { content: string; media_id?: string; channel_id?: string }) {
    return this.post('/posts', postData, { Authorization: `Bearer ${accessToken}` });
  }

  async getPost(accessToken: string, postId: string) {
    return this.get(`/posts/${postId}`, { Authorization: `Bearer ${accessToken}` });
  }

  async updatePost(accessToken: string, postId: string, updates: any) {
    return this.put(`/posts/${postId}`, updates, { Authorization: `Bearer ${accessToken}` });
  }

  async deletePost(accessToken: string, postId: string) {
    return this.delete(`/posts/${postId}`, { Authorization: `Bearer ${accessToken}` });
  }

  async likePost(accessToken: string, postId: string) {
    return this.post(`/posts/${postId}/like`, {}, { Authorization: `Bearer ${accessToken}` });
  }

  async unlikePost(accessToken: string, postId: string) {
    return this.delete(`/posts/${postId}/like`, { Authorization: `Bearer ${accessToken}` });
  }
}

export class MediaApiClient extends ApiClient {
  constructor() {
    super(undefined, true); // Use sync URL resolution
  }

  async uploadMedia(file: Buffer, filename: string, contentType: string) {
    return this.post('/media/upload', {
      filename,
      content_type: contentType,
      file_data: file.toString('base64')
    });
  }

  async getMedia(mediaId: string) {
    return this.get(`/media/${mediaId}`);
  }

  async deleteMedia(mediaId: string) {
    return this.delete(`/media/${mediaId}`);
  }
}

export class UsersApiClient extends ApiClient {
  constructor() {
    super(undefined, true); // Use sync URL resolution
  }

  async getProfile(userId?: string) {
    const path = userId ? `/users/profile/${userId}` : '/users/profile';
    return this.get(path);
  }

  async updateProfile(updates: any) {
    return this.put('/users/profile', updates);
  }

  async getUserPosts(userId: string, limit?: number, offset?: number) {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    const query = params.toString() ? `?${params.toString()}` : '';
    return this.get(`/users/posts/${userId}${query}`);
  }
}

// Helper functions for common test scenarios
export async function authenticateTestUser(email: string = '<EMAIL>', password: string = 'DevPassword123!') {
  const authClient = new AuthApiClient();
  const response = await authClient.signin(email, password);

  if (response.statusCode === 200 && response.body.tokens) {
    return {
      accessToken: response.body.tokens.access_token,
      refreshToken: response.body.tokens.refresh_token,
      idToken: response.body.tokens.id_token,
      user: response.body.user
    };
  }

  throw new Error(`Authentication failed: ${response.body.error || 'Unknown error'}`);
}

export async function createTestUser(email?: string, password?: string, username?: string) {
  const authClient = new AuthApiClient();

  const testEmail = email || `test_${Date.now()}@gameflex.com`;
  const testPassword = password || 'TestPassword123!';
  const testUsername = username || `testuser_${Date.now()}`;

  const response = await authClient.signup(testEmail, testPassword, testUsername);

  if (response.statusCode === 201) {
    return {
      email: testEmail,
      password: testPassword,
      username: testUsername,
      user: response.body.user
    };
  }

  throw new Error(`User creation failed: ${response.body.error || 'Unknown error'}`);
}

export function expectSuccessResponse(response: ApiResponse, expectedStatusCode: number = 200) {
  expect(response.statusCode).toBe(expectedStatusCode);
  expect(response.body).toBeDefined();
  expect(response.body.error).toBeUndefined();
}

export function expectErrorResponse(response: ApiResponse, expectedStatusCode: number, expectedError?: string) {
  expect(response.statusCode).toBe(expectedStatusCode);
  expect(response.body).toBeDefined();
  expect(response.body.error).toBeDefined();

  if (expectedError) {
    expect(response.body.error).toContain(expectedError);
  }
}

/**
 * Authenticate a user and return the access token
 */
export async function authenticateUser(email: string, password: string): Promise<string | null> {
  try {
    const apiGatewayUrl = await getApiGatewayUrl();
    const authClient = new ApiClient(apiGatewayUrl);

    const response = await authClient.post('/auth/signin', {
      email,
      password
    });

    if (response.statusCode === 200 && response.body.tokens && response.body.tokens.access_token) {
      return response.body.tokens.access_token;
    }

    console.error('Authentication failed:', response.statusCode, response.body);
    return null;
  } catch (error) {
    console.error('Authentication error:', error);
    return null;
  }
}

export function expectCorsHeaders(response: ApiResponse) {
  expect(response.headers['access-control-allow-origin']).toBe('*');
  expect(response.headers['access-control-allow-methods']).toContain('GET');
  expect(response.headers['access-control-allow-methods']).toContain('POST');
  expect(response.headers['access-control-allow-headers']).toContain('Content-Type');
  expect(response.headers['access-control-allow-headers']).toContain('Authorization');
}
