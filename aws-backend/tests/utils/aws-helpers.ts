/**
 * AWS service helpers for testing
 * Utilities for interacting with LocalStack services during tests
 */

import {
  DynamoDBClient,
  CreateTableCommand,
  DeleteTableCommand,
  DescribeTableCommand,
  ListTablesCommand
} from '@aws-sdk/client-dynamodb';
import {
  DynamoDBDocumentClient,
  PutCommand,
  DeleteCommand,
  Scan<PERSON>ommand
} from '@aws-sdk/lib-dynamodb';
import {
  S3Client,
  CreateBucketCommand,
  DeleteBucketCommand,
  ListObjectsV2Command,
  DeleteObjectCommand,
  PutObjectCommand
} from '@aws-sdk/client-s3';
import {
  CognitoIdentityProviderClient,
  CreateUserPoolCommand,
  DeleteUserPoolCommand,
  C<PERSON><PERSON>ser<PERSON>ool<PERSON>lientCommand,
  <PERSON>minCreateUserCommand,
  AdminDeleteUserCommand,
  ListUsersCommand
} from '@aws-sdk/client-cognito-identity-provider';
import {
  LambdaClient,
  InvokeCommand
} from '@aws-sdk/client-lambda';

// AWS clients configured for LocalStack
const dynamoClient = new DynamoDBClient({
  endpoint: process.env.AWS_ENDPOINT_URL,
  region: process.env.AWS_DEFAULT_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

const s3Client = new S3Client({
  endpoint: process.env.AWS_ENDPOINT_URL,
  region: process.env.AWS_DEFAULT_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  },
  forcePathStyle: true
});

const cognitoClient = new CognitoIdentityProviderClient({
  endpoint: process.env.AWS_ENDPOINT_URL,
  region: process.env.AWS_DEFAULT_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

const lambdaClient = new LambdaClient({
  endpoint: process.env.AWS_ENDPOINT_URL,
  region: process.env.AWS_DEFAULT_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
  }
});

export class DynamoDBHelper {
  static async createTable(tableName: string, keySchema: any[], attributeDefinitions: any[]) {
    try {
      await dynamoClient.send(new CreateTableCommand({
        TableName: tableName,
        KeySchema: keySchema,
        AttributeDefinitions: attributeDefinitions,
        BillingMode: 'PAY_PER_REQUEST'
      }));

      // Wait for table to be active
      await this.waitForTable(tableName);
    } catch (error: any) {
      if (error.name !== 'ResourceInUseException') {
        throw error;
      }
    }
  }

  static async deleteTable(tableName: string) {
    try {
      await dynamoClient.send(new DeleteTableCommand({
        TableName: tableName
      }));
    } catch (error: any) {
      if (error.name !== 'ResourceNotFoundException') {
        throw error;
      }
    }
  }

  static async waitForTable(tableName: string, maxAttempts: number = 30) {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const result = await dynamoClient.send(new DescribeTableCommand({
          TableName: tableName
        }));

        if (result.Table?.TableStatus === 'ACTIVE') {
          return;
        }
      } catch (error) {
        // Table doesn't exist yet
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error(`Table ${tableName} did not become active within ${maxAttempts} seconds`);
  }

  static async putItem(tableName: string, item: any) {
    return await docClient.send(new PutCommand({
      TableName: tableName,
      Item: item
    }));
  }

  static async deleteItem(tableName: string, key: any) {
    return await docClient.send(new DeleteCommand({
      TableName: tableName,
      Key: key
    }));
  }

  static async clearTable(tableName: string) {
    const result = await docClient.send(new ScanCommand({
      TableName: tableName
    }));

    if (result.Items) {
      for (const item of result.Items) {
        // Determine the key based on table name
        let key: any;
        if (tableName === 'Users') {
          key = { id: item.id };
        } else if (tableName === 'Posts') {
          key = { id: item.id };
        } else if (tableName === 'Media') {
          key = { id: item.id };
        } else {
          key = { id: item.id }; // Default
        }

        await this.deleteItem(tableName, key);
      }
    }
  }

  async cleanupTestUsers() {
    try {
      const usersTableName = process.env.DYNAMODB_TABLE_USERS || 'gameflex-development-Users';

      // Scan for test users (those with test emails)
      const result = await docClient.send(new ScanCommand({
        TableName: usersTableName,
        FilterExpression: 'contains(email, :testPattern1) OR contains(email, :testPattern2) OR contains(email, :testPattern3)',
        ExpressionAttributeValues: {
          ':testPattern1': 'test-',
          ':testPattern2': 'newuser-',
          ':testPattern3': 'forgetful-'
        }
      }));

      if (result.Items) {
        for (const item of result.Items) {
          await this.deleteItem(usersTableName, { id: item.id });
        }
      }
    } catch (error) {
      console.warn('Failed to cleanup test users:', error);
    }
  }
}

export class S3Helper {
  static async createBucket(bucketName: string) {
    try {
      await s3Client.send(new CreateBucketCommand({
        Bucket: bucketName
      }));
    } catch (error: any) {
      if (error.name !== 'BucketAlreadyOwnedByYou') {
        throw error;
      }
    }
  }

  static async deleteBucket(bucketName: string) {
    try {
      // First delete all objects
      await this.clearBucket(bucketName);

      // Then delete the bucket
      await s3Client.send(new DeleteBucketCommand({
        Bucket: bucketName
      }));
    } catch (error: any) {
      if (error.name !== 'NoSuchBucket') {
        throw error;
      }
    }
  }

  static async clearBucket(bucketName: string) {
    try {
      const objects = await s3Client.send(new ListObjectsV2Command({
        Bucket: bucketName
      }));

      if (objects.Contents) {
        for (const object of objects.Contents) {
          await s3Client.send(new DeleteObjectCommand({
            Bucket: bucketName,
            Key: object.Key!
          }));
        }
      }
    } catch (error: any) {
      if (error.name !== 'NoSuchBucket') {
        throw error;
      }
    }
  }

  static async putObject(bucketName: string, key: string, body: string | Buffer) {
    return await s3Client.send(new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: body
    }));
  }
}

export class CognitoHelper {
  static async createUserPool(poolName: string) {
    try {
      const result = await cognitoClient.send(new CreateUserPoolCommand({
        PoolName: poolName,
        Policies: {
          PasswordPolicy: {
            MinimumLength: 8,
            RequireUppercase: true,
            RequireLowercase: true,
            RequireNumbers: true,
            RequireSymbols: false
          }
        },
        AutoVerifiedAttributes: ['email']
      }));

      return result.UserPool?.Id;
    } catch (error) {
      console.error('Failed to create user pool:', error);
      throw error;
    }
  }

  static async deleteUserPool(userPoolId: string) {
    try {
      await cognitoClient.send(new DeleteUserPoolCommand({
        UserPoolId: userPoolId
      }));
    } catch (error: any) {
      if (error.name !== 'ResourceNotFoundException') {
        throw error;
      }
    }
  }

  static async createUserPoolClient(userPoolId: string, clientName: string) {
    try {
      const result = await cognitoClient.send(new CreateUserPoolClientCommand({
        UserPoolId: userPoolId,
        ClientName: clientName,
        ExplicitAuthFlows: ['ADMIN_NO_SRP_AUTH', 'ALLOW_REFRESH_TOKEN_AUTH']
      }));

      return result.UserPoolClient?.ClientId;
    } catch (error) {
      console.error('Failed to create user pool client:', error);
      throw error;
    }
  }

  static async createUser(userPoolId: string, email: string, password: string) {
    try {
      const result = await cognitoClient.send(new AdminCreateUserCommand({
        UserPoolId: userPoolId,
        Username: email,
        UserAttributes: [
          { Name: 'email', Value: email },
          { Name: 'email_verified', Value: 'true' }
        ],
        TemporaryPassword: password,
        MessageAction: 'SUPPRESS'
      }));

      return result.User?.Username;
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  }

  static async deleteUser(userPoolId: string, username: string) {
    try {
      await cognitoClient.send(new AdminDeleteUserCommand({
        UserPoolId: userPoolId,
        Username: username
      }));
    } catch (error: any) {
      if (error.name !== 'UserNotFoundException') {
        throw error;
      }
    }
  }

  static async clearUsers(userPoolId: string) {
    try {
      const users = await cognitoClient.send(new ListUsersCommand({
        UserPoolId: userPoolId
      }));

      if (users.Users) {
        for (const user of users.Users) {
          await this.deleteUser(userPoolId, user.Username!);
        }
      }
    } catch (error: any) {
      if (error.name !== 'ResourceNotFoundException') {
        throw error;
      }
    }
  }
}

export class LambdaHelper {
  static async invokeFunction(functionName: string, payload: any) {
    const result = await lambdaClient.send(new InvokeCommand({
      FunctionName: functionName,
      Payload: JSON.stringify(payload)
    }));

    if (result.Payload) {
      const response = JSON.parse(Buffer.from(result.Payload).toString());
      return response;
    }

    return null;
  }
}
