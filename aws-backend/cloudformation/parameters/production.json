[{"ParameterKey": "Environment", "ParameterValue": "production"}, {"ParameterKey": "ProjectName", "ParameterValue": "gameflex"}, {"ParameterKey": "DomainName", "ParameterValue": "gameflex.io"}, {"ParameterKey": "ApiDomainName", "ParameterValue": "api.gameflex.io"}, {"ParameterKey": "CertificateArn", "ParameterValue": "arn:aws:acm:us-east-1:ACCOUNT_ID:certificate/CERTIFICATE_ID"}, {"ParameterKey": "EnableCloudFront", "ParameterValue": "true"}, {"ParameterKey": "EnableWAF", "ParameterValue": "true"}, {"ParameterKey": "EnableXRay", "ParameterValue": "true"}, {"ParameterKey": "LambdaMemorySize", "ParameterValue": "1024"}, {"ParameterKey": "LambdaTimeout", "ParameterValue": "60"}, {"ParameterKey": "S3BucketVersioning", "ParameterValue": "Enabled"}, {"ParameterKey": "S3BucketEncryption", "ParameterValue": "true"}, {"ParameterKey": "EnableS3AccessLogging", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordMinLength", "ParameterValue": "10"}, {"ParameterKey": "CognitoPasswordRequireUppercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireLowercase", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireNumbers", "ParameterValue": "true"}, {"ParameterKey": "CognitoPasswordRequireSymbols", "ParameterValue": "true"}, {"ParameterKey": "CognitoMfaConfiguration", "ParameterValue": "ON"}, {"ParameterKey": "ApiGatewayThrottleBurstLimit", "ParameterValue": "2000"}, {"ParameterKey": "ApiGatewayThrottleRateLimit", "ParameterValue": "1000"}, {"ParameterKey": "EnableApiGatewayLogging", "ParameterValue": "true"}, {"ParameterKey": "ApiGatewayLogLevel", "ParameterValue": "ERROR"}, {"ParameterKey": "EnableDetailedMetrics", "ParameterValue": "true"}, {"ParameterKey": "AlertingEmail", "ParameterValue": "<EMAIL>"}, {"ParameterKey": "Enable<PERSON><PERSON><PERSON>", "ParameterValue": "true"}, {"ParameterKey": "LogRetentionDays", "ParameterValue": "90"}, {"ParameterKey": "EnableVPCEndpoints", "ParameterValue": "true"}, {"ParameterKey": "VPCCidr", "ParameterValue": "10.3.0.0/16"}, {"ParameterKey": "PublicSubnet1Cidr", "ParameterValue": "10.3.1.0/24"}, {"ParameterKey": "PublicSubnet2Cidr", "ParameterValue": "10.3.2.0/24"}, {"ParameterKey": "PrivateSubnet1Cidr", "ParameterValue": "10.3.3.0/24"}, {"ParameterKey": "PrivateSubnet2Cidr", "ParameterValue": "10.3.4.0/24"}]