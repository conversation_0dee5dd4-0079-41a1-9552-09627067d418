# GameFlex AWS Backend Deployment Guide

This guide explains the different deployment approaches available for the GameFlex AWS backend and when to use each one.

## ⚠️ Important: API Gateway Deployment

**Only ONE deployment approach should be used at a time to avoid creating multiple API Gateways.**

## Deployment Options

### 1. Full Infrastructure with API Gateway (Recommended)

**Script:** `start.ps1`
**Template:** `cloudformation/gameflex-infrastructure.yaml`
**Stack Name:** `gameflex-infrastructure-development`

**Includes:**
- ✅ AWS API Gateway with all endpoints
- ✅ Cognito User Pool and Identity Pool
- ✅ Lambda Functions (Auth, Posts, Media, Users)
- ✅ DynamoDB Tables
- ✅ S3 Buckets
- ✅ IAM Roles and Policies

**Use this when:**
- You need a complete backend with API endpoints
- You're developing the Flutter app that needs to call APIs
- You want the full GameFlex backend experience

**Command:**
```powershell
.\start.ps1
```

### 2. Simple Infrastructure without API Gateway

**Script:** `build-and-deploy.ps1`
**Template:** `cloudformation/gameflex-simple-infrastructure.yaml`
**Stack Name:** `gameflex-simple-infrastructure-development`

**Includes:**
- ❌ No API Gateway
- ✅ Lambda Functions (limited)
- ✅ DynamoDB Tables
- ✅ S3 Buckets
- ✅ IAM Roles

**Use this when:**
- You only need backend storage and compute
- You're testing Lambda functions directly
- You don't need HTTP API endpoints
- You're doing containerized deployments

**Command:**
```powershell
.\build-and-deploy.ps1
```

## Stack Names and Conflicts

To prevent conflicts, the deployment scripts use different stack names:

- **start.ps1**: `gameflex-infrastructure-development`
- **build-and-deploy.ps1**: `gameflex-simple-infrastructure-development`

This ensures that both approaches can coexist without interfering with each other.

## Checking Current Deployment

To see which stacks are currently deployed:

```powershell
# Check for full infrastructure stack
aws --endpoint-url=http://localhost:45660 cloudformation describe-stacks --stack-name gameflex-infrastructure-development

# Check for simple infrastructure stack
aws --endpoint-url=http://localhost:45660 cloudformation describe-stacks --stack-name gameflex-simple-infrastructure-development

# List all API Gateways
aws --endpoint-url=http://localhost:45660 apigateway get-rest-apis
```

## Cleanup

To clean up deployments:

```powershell
# Stop and clean up full infrastructure
.\stop.ps1

# Or manually delete stacks
aws --endpoint-url=http://localhost:45660 cloudformation delete-stack --stack-name gameflex-infrastructure-development
aws --endpoint-url=http://localhost:45660 cloudformation delete-stack --stack-name gameflex-simple-infrastructure-development
```

## Troubleshooting Multiple API Gateways

If you see multiple API Gateways:

1. **Check which stacks are deployed:**
   ```powershell
   aws --endpoint-url=http://localhost:45660 cloudformation list-stacks --stack-status-filter CREATE_COMPLETE UPDATE_COMPLETE
   ```

2. **List all API Gateways:**
   ```powershell
   aws --endpoint-url=http://localhost:45660 apigateway get-rest-apis
   ```

3. **Delete unwanted stacks:**
   ```powershell
   aws --endpoint-url=http://localhost:45660 cloudformation delete-stack --stack-name <unwanted-stack-name>
   ```

4. **Restart with clean slate:**
   ```powershell
   .\stop.ps1
   docker system prune -f
   .\start.ps1
   ```

## Recommended Workflow

For GameFlex development:

1. **Use start.ps1 for full development:**
   ```powershell
   .\start.ps1
   ```

2. **Test your Flutter app against the API Gateway**

3. **Use build-and-deploy.ps1 only for:**
   - Lambda function testing
   - Storage-only operations
   - Containerized deployments without API needs

## Environment Variables

Both approaches use the same environment variables from `.env`:

- `LOCALSTACK_AUTH_TOKEN`
- `AWS_ACCESS_KEY_ID=test`
- `AWS_SECRET_ACCESS_KEY=test`
- `AWS_DEFAULT_REGION=us-east-1`
