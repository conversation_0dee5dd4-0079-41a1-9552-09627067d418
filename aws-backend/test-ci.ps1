# GameFlex AWS Backend CI Test Script
# Automated testing script for continuous integration

param(
    [switch]$Quick,
    [switch]$SkipE2E,
    [string]$OutputFormat = "junit",
    [string]$OutputDir = "test-results"
)

function Write-Status {
    param([string]$Message)
    Write-Host "[CI] $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "[CI-ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[CI-TEST] $Message" -ForegroundColor Blue
}

function Setup-TestEnvironment {
    Write-Header "Setting up CI Test Environment"
    
    # Create output directory
    if (-not (Test-Path $OutputDir)) {
        New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
        Write-Status "Created output directory: $OutputDir"
    }
    
    # Set CI environment variables
    $env:CI = "true"
    $env:NODE_ENV = "test"
    $env:DEBUG_TESTS = "false"  # Reduce noise in CI
    
    Write-Status "Environment configured for CI"
    return $true
}

function Run-LintChecks {
    Write-Header "Running Code Quality Checks"
    
    # Check if ESLint is available
    if (Get-Command "npx" -ErrorAction SilentlyContinue) {
        # Run TypeScript compiler check
        Write-Status "Running TypeScript compiler check..."
        npx tsc --noEmit
        if ($LASTEXITCODE -ne 0) {
            Write-Error "TypeScript compilation check failed"
            return $false
        }
        
        # Run ESLint if config exists
        if (Test-Path ".eslintrc*") {
            Write-Status "Running ESLint..."
            npx eslint . --ext .ts --format json --output-file "$OutputDir/eslint-results.json"
            if ($LASTEXITCODE -ne 0) {
                Write-Error "ESLint found issues"
                return $false
            }
        }
    }
    
    Write-Status "Code quality checks passed"
    return $true
}

function Run-UnitTests {
    Write-Header "Running Unit Tests"
    
    $jestArgs = @(
        "--testPathPattern=unit",
        "--coverage",
        "--coverageDirectory=$OutputDir/coverage",
        "--coverageReporters=text,lcov,cobertura,json",
        "--testResultsProcessor=jest-junit",
        "--ci",
        "--watchAll=false"
    )
    
    # Set Jest JUnit output
    $env:JEST_JUNIT_OUTPUT_DIR = $OutputDir
    $env:JEST_JUNIT_OUTPUT_NAME = "unit-test-results.xml"
    
    $jestCommand = "npx jest " + ($jestArgs -join " ")
    Write-Status "Executing: $jestCommand"
    
    Invoke-Expression $jestCommand
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Status "Unit tests passed"
        return $true
    } else {
        Write-Error "Unit tests failed with exit code: $exitCode"
        return $false
    }
}

function Run-IntegrationTests {
    Write-Header "Running Integration Tests"
    
    $jestArgs = @(
        "--testPathPattern=integration",
        "--testResultsProcessor=jest-junit",
        "--ci",
        "--watchAll=false",
        "--testTimeout=60000"
    )
    
    # Set Jest JUnit output
    $env:JEST_JUNIT_OUTPUT_NAME = "integration-test-results.xml"
    
    $jestCommand = "npx jest " + ($jestArgs -join " ")
    Write-Status "Executing: $jestCommand"
    
    Invoke-Expression $jestCommand
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Status "Integration tests passed"
        return $true
    } else {
        Write-Error "Integration tests failed with exit code: $exitCode"
        return $false
    }
}

function Run-ApiTests {
    Write-Header "Running API Tests"
    
    $jestArgs = @(
        "--testPathPattern=api",
        "--testResultsProcessor=jest-junit",
        "--ci",
        "--watchAll=false",
        "--testTimeout=60000"
    )
    
    # Set Jest JUnit output
    $env:JEST_JUNIT_OUTPUT_NAME = "api-test-results.xml"
    
    $jestCommand = "npx jest " + ($jestArgs -join " ")
    Write-Status "Executing: $jestCommand"
    
    Invoke-Expression $jestCommand
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Status "API tests passed"
        return $true
    } else {
        Write-Error "API tests failed with exit code: $exitCode"
        return $false
    }
}

function Run-E2ETests {
    Write-Header "Running End-to-End Tests"
    
    $jestArgs = @(
        "--testPathPattern=e2e",
        "--testResultsProcessor=jest-junit",
        "--ci",
        "--watchAll=false",
        "--testTimeout=120000",
        "--maxWorkers=1"  # Run E2E tests sequentially
    )
    
    # Set Jest JUnit output
    $env:JEST_JUNIT_OUTPUT_NAME = "e2e-test-results.xml"
    
    $jestCommand = "npx jest " + ($jestArgs -join " ")
    Write-Status "Executing: $jestCommand"
    
    Invoke-Expression $jestCommand
    $exitCode = $LASTEXITCODE
    
    if ($exitCode -eq 0) {
        Write-Status "E2E tests passed"
        return $true
    } else {
        Write-Error "E2E tests failed with exit code: $exitCode"
        return $false
    }
}

function Generate-TestReport {
    Write-Header "Generating Test Report"
    
    $reportData = @{
        timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ssZ"
        environment = "CI"
        results = @{}
    }
    
    # Collect test results
    $testFiles = Get-ChildItem -Path $OutputDir -Filter "*test-results.xml"
    foreach ($file in $testFiles) {
        $testType = $file.Name -replace "-test-results.xml", ""
        $reportData.results[$testType] = @{
            file = $file.Name
            path = $file.FullName
        }
    }
    
    # Collect coverage data
    if (Test-Path "$OutputDir/coverage/coverage-summary.json") {
        $coverageData = Get-Content "$OutputDir/coverage/coverage-summary.json" | ConvertFrom-Json
        $reportData.coverage = $coverageData.total
    }
    
    # Save report
    $reportData | ConvertTo-Json -Depth 10 | Out-File "$OutputDir/test-report.json" -Encoding UTF8
    
    Write-Status "Test report generated: $OutputDir/test-report.json"
}

function Show-CISummary {
    param([bool[]]$TestResults)
    
    Write-Host ""
    Write-Header "CI Test Summary"
    Write-Host ""
    
    $totalTests = $TestResults.Count
    $passedTests = ($TestResults | Where-Object { $_ -eq $true }).Count
    $failedTests = $totalTests - $passedTests
    
    Write-Host "Total Test Suites: $totalTests" -ForegroundColor Cyan
    Write-Host "Passed: $passedTests" -ForegroundColor Green
    Write-Host "Failed: $failedTests" -ForegroundColor $(if ($failedTests -eq 0) { "Green" } else { "Red" })
    
    # Show coverage summary if available
    if (Test-Path "$OutputDir/coverage/coverage-summary.json") {
        try {
            $coverageSummary = Get-Content "$OutputDir/coverage/coverage-summary.json" | ConvertFrom-Json
            $totalCoverage = $coverageSummary.total
            
            Write-Host ""
            Write-Host "Coverage Summary:" -ForegroundColor Cyan
            Write-Host "  Lines: $($totalCoverage.lines.pct)%" -ForegroundColor $(if ($totalCoverage.lines.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Functions: $($totalCoverage.functions.pct)%" -ForegroundColor $(if ($totalCoverage.functions.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Branches: $($totalCoverage.branches.pct)%" -ForegroundColor $(if ($totalCoverage.branches.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Statements: $($totalCoverage.statements.pct)%" -ForegroundColor $(if ($totalCoverage.statements.pct -ge 80) { "Green" } else { "Yellow" })
        } catch {
            Write-Warning "Could not parse coverage summary"
        }
    }
    
    Write-Host ""
    Write-Status "Test artifacts saved to: $OutputDir"
    
    return $failedTests -eq 0
}

function Main {
    Write-Header "GameFlex AWS Backend CI Test Pipeline"
    Write-Host ""
    Write-Status "Quick Mode: $Quick"
    Write-Status "Skip E2E: $SkipE2E"
    Write-Status "Output Format: $OutputFormat"
    Write-Status "Output Directory: $OutputDir"
    Write-Host ""
    
    # Setup
    if (-not (Setup-TestEnvironment)) {
        Write-Error "Failed to setup test environment"
        exit 1
    }
    
    # Install dependencies and build
    Write-Status "Running setup..."
    & "./test-suite.ps1" -SetupOnly
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Setup failed"
        exit 1
    }
    
    $testResults = @()
    
    # Run code quality checks
    if (-not $Quick) {
        $testResults += Run-LintChecks
    }
    
    # Run unit tests (always run these)
    $testResults += Run-UnitTests
    
    # Run integration tests
    if (-not $Quick) {
        $testResults += Run-IntegrationTests
    }
    
    # Run API tests
    $testResults += Run-ApiTests
    
    # Run E2E tests
    if (-not $SkipE2E -and -not $Quick) {
        $testResults += Run-E2ETests
    }
    
    # Generate report
    Generate-TestReport
    
    # Show summary and exit
    $success = Show-CISummary -TestResults $testResults
    
    if ($success) {
        Write-Status "All CI tests passed!"
        exit 0
    } else {
        Write-Error "Some CI tests failed"
        exit 1
    }
}

# Show help if requested
if ($args -contains "--help" -or $args -contains "-h") {
    Write-Host "GameFlex AWS Backend CI Test Pipeline"
    Write-Host ""
    Write-Host "Usage: ./test-ci.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -Quick              Run only essential tests (unit + api)"
    Write-Host "  -SkipE2E            Skip end-to-end tests"
    Write-Host "  -OutputFormat       Test output format (junit, json) [default: junit]"
    Write-Host "  -OutputDir          Directory for test artifacts [default: test-results]"
    Write-Host "  -h, --help          Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  ./test-ci.ps1                    # Full CI pipeline"
    Write-Host "  ./test-ci.ps1 -Quick             # Quick tests only"
    Write-Host "  ./test-ci.ps1 -SkipE2E           # Skip E2E tests"
    exit 0
}

# Run main function
try {
    Main
} catch {
    Write-Error "CI test pipeline failed: $_"
    exit 1
}
