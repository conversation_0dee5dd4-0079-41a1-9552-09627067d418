# GameFlex AWS Backend Stop Script (PowerShell)
# This script stops the AWS backend services (Docker-based LocalStack Pro)

param(
    [switch]$Force,
    [switch]$RemoveVolumes,
    [switch]$Verbose,
    [switch]$CleanAll
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Load environment variables from .env file
function Load-EnvFile {
    if (Test-Path ".env") {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^\s*([^#][^=]*)\s*=\s*(.*)\s*$") {
                $name = $matches[1].Trim()
                $value = $matches[2].Trim()
                # Remove quotes if present
                $value = $value -replace '^"(.*)"$', '$1'
                $value = $value -replace "^'(.*)'$", '$1'
                [Environment]::SetEnvironmentVariable($name, $value, "Process")
            }
        }
    }
}

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[GAMEFLEX] $Message" -ForegroundColor Blue
}

# Check if Docker Desktop is running
function Test-Docker {
    try {
        docker info | Out-Null
        Write-Status "Docker Desktop is running"
        return $true
    }
    catch {
        Write-Warning "Docker Desktop is not running or not accessible"
        return $false
    }
}

# Stop Docker services
function Stop-Services {
    Write-Header "Stopping GameFlex AWS Backend..."
    
    try {
        # Check if services are running
        $runningServices = docker compose ps --services --filter "status=running" 2>$null
        
        if (-not $runningServices) {
            Write-Warning "No running services found"
            return $true
        }
        
        Write-Status "Stopping services..."
        docker compose down
        
        if ($RemoveVolumes) {
            Write-Status "Removing volumes..."
            docker compose down -v
            
            # Remove named volumes (LocalStack only)
            $volumes = @(
                # No additional volumes needed - LocalStack handles persistence
            )
            
            foreach ($volume in $volumes) {
                try {
                    docker volume rm $volume 2>$null
                    Write-Status "Removed volume: $volume"
                }
                catch {
                    Write-Warning "Could not remove volume: $volume (may not exist)"
                }
            }
        }
        
        # Force remove containers if requested
        if ($Force) {
            Write-Status "Force removing containers..."
            $containers = docker compose ps -aq 2>$null
            if ($containers) {
                docker rm -f $containers 2>$null
            }
        }

        # Clean all Docker resources if requested
        if ($CleanAll) {
            Write-Status "Cleaning all Docker resources..."
            docker system prune -f
            Write-Status "Removing unused images..."
            docker image prune -f
        }
        
        Write-Status "Services stopped successfully"
        return $true
    }
    catch {
        Write-Error "Failed to stop services: $_"
        return $false
    }
}

# Clean up temporary files
function Remove-TempFiles {
    Write-Status "Cleaning up temporary files..."
    
    $tempPaths = @(
        "logs\*.log",
        "tmp\*"
    )
    
    foreach ($path in $tempPaths) {
        if (Test-Path $path) {
            try {
                Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
                Write-Status "Cleaned up: $path"
            }
            catch {
                Write-Warning "Could not clean up: $path"
            }
        }
    }
}

# Display cleanup information
function Show-CleanupInfo {
    Write-Header "GameFlex AWS Backend has been stopped"
    Write-Host ""
    
    Write-Status "What was stopped:"
    Write-Host "  🛑 LocalStack Pro container" -ForegroundColor Cyan
    Write-Host "  🛑 Lambda builder containers" -ForegroundColor Cyan
    Write-Host "  🛑 AWS initializer containers" -ForegroundColor Cyan
    Write-Host ""

    if ($RemoveVolumes) {
        Write-Warning "Data volumes were removed - all data has been lost!"
    }
    else {
        Write-Status "Data volumes preserved - data will be available on next startup"
    }

    if ($CleanAll) {
        Write-Warning "Docker system was cleaned - all unused resources removed!"
    }

    Write-Host ""
    Write-Status "To restart the backend:"
    Write-Host "  ▶️  Run: .\start.ps1" -ForegroundColor Cyan
    Write-Host "  ▶️  Build only: .\start.ps1 -BuildOnly" -ForegroundColor Cyan
    Write-Host "  ▶️  Skip build: .\start.ps1 -SkipBuild" -ForegroundColor Cyan
    Write-Host ""

    Write-Status "To completely reset:"
    Write-Host "  🗑️  Remove data: .\stop.ps1 -RemoveVolumes" -ForegroundColor Cyan
    Write-Host "  🗑️  Clean all: .\stop.ps1 -CleanAll" -ForegroundColor Cyan
}

# Check for running processes that might interfere
function Test-RunningProcesses {
    $processes = @()
    
    # Check for processes using our ports
    $ports = @(4566, 5433, 6380)
    foreach ($port in $ports) {
        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        if ($connections) {
            $processes += "Port $port is still in use"
        }
    }
    
    if ($processes.Count -gt 0) {
        Write-Warning "Some processes may still be running:"
        foreach ($process in $processes) {
            Write-Host "  ⚠️  $process" -ForegroundColor Yellow
        }
        Write-Host ""
    }
}

# Main execution
function Main {
    Write-Header "GameFlex AWS Backend Shutdown"
    Write-Host ""

    # Load environment variables from .env file
    Load-EnvFile

    if (-not (Test-Docker)) {
        Write-Warning "Docker is not accessible, but continuing with cleanup..."
    }
    
    if (-not (Stop-Services)) {
        Write-Error "Failed to stop services properly"
        exit 1
    }
    
    Remove-TempFiles
    Test-RunningProcesses
    Show-CleanupInfo
    
    Write-Status "Shutdown completed successfully!"
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex AWS Backend Stop Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\stop.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Force           Force remove containers"
    Write-Host "  -RemoveVolumes   Remove data volumes (WARNING: This will delete all data!)"
    Write-Host "  -Verbose         Show verbose output"
    Write-Host "  -h, --help       Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\stop.ps1                    # Normal stop"
    Write-Host "  .\stop.ps1 -Force             # Force stop containers"
    Write-Host "  .\stop.ps1 -RemoveVolumes     # Stop and remove all data"
    exit 0
}

# Run main function
try {
    Main
}
catch {
    Write-Error "Shutdown failed: $_"
    exit 1
}
