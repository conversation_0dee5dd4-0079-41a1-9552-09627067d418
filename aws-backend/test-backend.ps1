# GameFlex AWS Backend Test Script (PowerShell)
# This script tests the AWS backend API endpoints

param(
    [string]$BaseUrl = "http://localhost:45660/restapis",
    [string]$Stage = "development",
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
}

function Write-Failure {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
}

# Global variables for test state
$script:ApiGatewayId = $null
$script:AccessToken = $null
$script:TestResults = @{
    Passed = 0
    Failed = 0
    Tests  = @()
}

# Get API Gateway ID
function Get-ApiGatewayId {
    try {
        Write-Status "Getting API Gateway ID..."

        $result = aws --endpoint-url=http://localhost:45660 apigateway get-rest-apis --query "items[?name=='gameflex-api-development'].id" --output text

        if ($result -and $result.Trim() -ne "") {
            $script:ApiGatewayId = $result.Trim()
            Write-Status "Found API Gateway ID: $script:ApiGatewayId"
            return $true
        }
        else {
            Write-Error "API Gateway not found"
            return $false
        }
    }
    catch {
        Write-Error "Failed to get API Gateway ID: $_"
        return $false
    }
}

# Make HTTP request
function Invoke-ApiRequest {
    param(
        [string]$Method,
        [string]$Path,
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )

    $url = "$BaseUrl/$script:ApiGatewayId/$Stage/_user_request_$Path"
    
    try {
        $requestParams = @{
            Uri         = $url
            Method      = $Method
            Headers     = $Headers
            ContentType = "application/json"
        }
        
        if ($Body) {
            $requestParams.Body = $Body
        }
        
        if ($Verbose) {
            Write-Host "Request: $Method $url" -ForegroundColor Cyan
            if ($Body) {
                Write-Host "Body: $Body" -ForegroundColor Cyan
            }
        }
        
        $response = Invoke-RestMethod @requestParams
        return @{
            Success    = $true
            Data       = $response
            StatusCode = 200
        }
    }
    catch {
        $statusCode = $_.Exception.Response.StatusCode.value__
        $errorBody = $_.ErrorDetails.Message
        
        if ($Verbose) {
            Write-Host "Error: $statusCode - $errorBody" -ForegroundColor Red
        }
        
        return @{
            Success    = $false
            Error      = $_.Exception.Message
            StatusCode = $statusCode
            ErrorBody  = $errorBody
        }
    }
}

# Test function wrapper
function Test-Endpoint {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    Write-Host ""
    Write-Header "Testing: $TestName"
    
    try {
        $result = & $TestScript
        
        if ($result) {
            Write-Success "$TestName - PASSED"
            $script:TestResults.Passed++
        }
        else {
            Write-Failure "$TestName - FAILED"
            $script:TestResults.Failed++
        }
        
        $script:TestResults.Tests += @{
            Name   = $TestName
            Passed = $result
        }
        
        return $result
    }
    catch {
        Write-Failure "$TestName - ERROR: $_"
        $script:TestResults.Failed++
        $script:TestResults.Tests += @{
            Name   = $TestName
            Passed = $false
            Error  = $_.Exception.Message
        }
        return $false
    }
}

# Test user signup
function Test-UserSignup {
    $testEmail = "test_$(Get-Random)@example.com"
    $testPassword = "TestPassword123!"
    $testUsername = "testuser_$(Get-Random)"
    
    $body = @{
        email    = $testEmail
        password = $testPassword
        username = $testUsername
    } | ConvertTo-Json
    
    $response = Invoke-ApiRequest -Method "POST" -Path "/auth/signup" -Body $body
    
    if ($response.Success) {
        Write-Status "Signup successful for $testEmail"
        return $true
    }
    else {
        Write-Error "Signup failed: $($response.Error)"
        return $false
    }
}

# Test user signin
function Test-UserSignin {
    # Use the dev user from seed data
    $body = @{
        email    = "<EMAIL>"
        password = "GameFlex123!"
    } | ConvertTo-Json
    
    $response = Invoke-ApiRequest -Method "POST" -Path "/auth/signin" -Body $body
    
    if ($response.Success -and $response.Data.tokens.access_token) {
        $script:AccessToken = $response.Data.tokens.access_token
        Write-Status "Signin successful, access token obtained"
        return $true
    }
    else {
        Write-Error "Signin failed: $($response.Error)"
        return $false
    }
}

# Test get posts
function Test-GetPosts {
    $response = Invoke-ApiRequest -Method "GET" -Path "/posts"
    
    if ($response.Success -and $response.Data.posts) {
        Write-Status "Retrieved $($response.Data.posts.Count) posts"
        return $true
    }
    else {
        Write-Error "Get posts failed: $($response.Error)"
        return $false
    }
}

# Test create post
function Test-CreatePost {
    if (-not $script:AccessToken) {
        Write-Error "No access token available for authenticated request"
        return $false
    }
    
    $headers = @{
        "Authorization" = "Bearer $script:AccessToken"
    }
    
    $body = @{
        content = "Test post created at $(Get-Date)"
    } | ConvertTo-Json
    
    $response = Invoke-ApiRequest -Method "POST" -Path "/posts" -Headers $headers -Body $body
    
    if ($response.Success -and $response.Data.post.id) {
        Write-Status "Post created successfully with ID: $($response.Data.post.id)"
        return $true
    }
    else {
        Write-Error "Create post failed: $($response.Error)"
        return $false
    }
}

# Test LocalStack health
function Test-LocalStackHealth {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -Method GET
        
        if ($response.services) {
            Write-Status "LocalStack is healthy"
            return $true
        }
        else {
            Write-Error "LocalStack health check failed"
            return $false
        }
    }
    catch {
        Write-Error "LocalStack is not accessible: $_"
        return $false
    }
}

# Test RDS connection via LocalStack
function Test-DatabaseConnection {
    try {
        # Test RDS cluster existence
        $clusters = aws --endpoint-url=$BaseUrl rds describe-db-clusters --query "DBClusters[?DBClusterIdentifier=='gameflex-cluster'].Status" --output text 2>$null

        if ($LASTEXITCODE -eq 0 -and $clusters) {
            Write-Status "RDS cluster is accessible via LocalStack"
            return $true
        }
        else {
            Write-Warning "RDS cluster not found, but LocalStack RDS service is available"
            return $true
        }
    }
    catch {
        Write-Error "RDS test failed: $_"
        return $false
    }
}

# Test S3 buckets
function Test-S3Buckets {
    try {
        $buckets = aws --endpoint-url=http://localhost:45660 s3 ls --output text
        
        if ($buckets -match "gameflex-media" -and $buckets -match "gameflex-avatars") {
            Write-Status "S3 buckets are available"
            return $true
        }
        else {
            Write-Error "Required S3 buckets not found"
            return $false
        }
    }
    catch {
        Write-Error "S3 test failed: $_"
        return $false
    }
}

# Display test results
function Show-TestResults {
    Write-Host ""
    Write-Header "Test Results Summary"
    Write-Host ""
    
    $total = $script:TestResults.Passed + $script:TestResults.Failed
    $passRate = if ($total -gt 0) { [math]::Round(($script:TestResults.Passed / $total) * 100, 1) } else { 0 }
    
    Write-Host "Total Tests: $total" -ForegroundColor Cyan
    Write-Host "Passed: $($script:TestResults.Passed)" -ForegroundColor Green
    Write-Host "Failed: $($script:TestResults.Failed)" -ForegroundColor Red
    Write-Host "Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Yellow" })
    
    Write-Host ""
    Write-Status "Individual Test Results:"
    
    foreach ($test in $script:TestResults.Tests) {
        $status = if ($test.Passed) { "PASS" } else { "FAIL" }
        $color = if ($test.Passed) { "Green" } else { "Red" }
        Write-Host "  $status - $($test.Name)" -ForegroundColor $color
        
        if ($test.Error) {
            Write-Host "    Error: $($test.Error)" -ForegroundColor Red
        }
    }
}

# Main test execution
function Main {
    Write-Header "GameFlex AWS Backend Test Suite"
    Write-Host ""
    
    # Prerequisites
    Write-Status "Checking prerequisites..."
    
    # Test LocalStack
    Test-Endpoint "LocalStack Health Check" { Test-LocalStackHealth }
    
    # Test Database
    Test-Endpoint "Database Connection" { Test-DatabaseConnection }
    
    # Test S3
    Test-Endpoint "S3 Buckets" { Test-S3Buckets }
    
    # Get API Gateway ID
    if (-not (Get-ApiGatewayId)) {
        Write-Error "Cannot proceed without API Gateway ID"
        Show-TestResults
        exit 1
    }
    
    # API Tests
    Write-Status "Running API tests..."
    
    # Test authentication endpoints
    Test-Endpoint "User Signup" { Test-UserSignup }
    Test-Endpoint "User Signin" { Test-UserSignin }
    
    # Test posts endpoints
    Test-Endpoint "Get Posts" { Test-GetPosts }
    Test-Endpoint "Create Post" { Test-CreatePost }
    
    # Show results
    Show-TestResults
    
    # Exit with appropriate code
    if ($script:TestResults.Failed -eq 0) {
        Write-Status "All tests passed!"
        exit 0
    }
    else {
        Write-Error "Some tests failed"
        exit 1
    }
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex AWS Backend Test Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\test-backend.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -BaseUrl      API Gateway base URL (default: http://localhost:45660/restapis)"
    Write-Host "  -Stage        API Gateway stage (default: development)"
    Write-Host "  -Verbose      Show verbose output"
    Write-Host "  -h, --help    Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\test-backend.ps1"
    Write-Host "  .\test-backend.ps1 -Verbose"
    Write-Host "  .\test-backend.ps1 -Stage production"
    exit 0
}

# Run tests
try {
    Main
}
catch {
    Write-Error "Test execution failed: $_"
    exit 1
}
