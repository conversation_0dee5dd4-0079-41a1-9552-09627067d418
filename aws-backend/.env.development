# GameFlex AWS Backend Environment Configuration - Development
# This file contains development-specific environment variables

############
# Environment Configuration
############
APP_ENVIRONMENT=development
NODE_ENV=development
DEBUG=true

############
# AWS Configuration - LocalStack
############
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_ENDPOINT_URL=http://localhost:4566

############
# LocalStack Configuration
############
LOCALSTACK_HOST=localhost:4566
LOCALSTACK_HOSTNAME=localhost
LOCALSTACK_DEBUG=1
LOCALSTACK_PERSISTENCE=1

# LocalStack Pro Auth Token (required for Pro features)
# Get your token from: https://app.localstack.cloud/
LOCALSTACK_AUTH_TOKEN=ls-NIdUsIdA-lIza-5087-Fuji-jorOpuPAb667

# Docker container name
LOCALSTACK_DOCKER_NAME=gameflex-localstack

# Volume directory for persistence
LOCALSTACK_VOLUME_DIR=./volume

############
# Database Configuration (LocalStack RDS)
############
DB_HOST=localhost
DB_PORT=4566
DB_NAME=gameflex
DB_USER=postgres
DB_PASSWORD=gameflex_password
DB_SSL_MODE=disable
DB_ENDPOINT_URL=http://localhost:4566

############
# Cognito Configuration (Auto-populated during initialization)
############
COGNITO_USER_POOL_ID=
COGNITO_USER_POOL_CLIENT_ID=
COGNITO_IDENTITY_POOL_ID=

############
# S3 Configuration
############
S3_BUCKET_MEDIA=gameflex-media-development
S3_BUCKET_AVATARS=gameflex-avatars-development
S3_BUCKET_TEMP=gameflex-temp-development
S3_ENDPOINT_URL=http://localhost:4566

############
# API Gateway Configuration
############
API_GATEWAY_URL=http://localhost:4566
API_GATEWAY_STAGE=development

############
# Lambda Configuration
############
LAMBDA_RUNTIME=nodejs18.x
LAMBDA_TIMEOUT=30
LAMBDA_MEMORY_SIZE=256

############
# Application Configuration
############
APP_NAME=GameFlex
APP_VERSION=1.0.0
PROJECT_NAME=gameflex

# JWT Configuration
JWT_SECRET=dev-super-secret-jwt-token-with-at-least-32-characters-long
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080,http://********:8080,http://gameflex.local:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token

############
# File Upload Configuration
############
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,mp4,mov,avi
UPLOAD_PATH=uploads

############
# Email Configuration (for Cognito)
############
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

############
# Logging Configuration
############
LOG_LEVEL=DEBUG
LOG_FORMAT=json
ENABLE_DEBUG_LOGS=true

############
# Security Configuration
############
BCRYPT_ROUNDS=10
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900

############
# Development Configuration
############
ENABLE_CORS=true
ENABLE_SWAGGER=true
ENABLE_HOT_RELOAD=true
ENABLE_MOCK_DATA=true

############
# Monitoring and Alerting
############
ENABLE_METRICS=false
ENABLE_TRACING=false
ENABLE_ALERTS=false
ALERTING_EMAIL=<EMAIL>

############
# Performance Configuration
############
ENABLE_CACHING=false
CACHE_TTL=300
RATE_LIMIT_ENABLED=false
RATE_LIMIT_REQUESTS_PER_MINUTE=1000

############
# Feature Flags
############
FEATURE_USER_REGISTRATION=true
FEATURE_SOCIAL_LOGIN=false
FEATURE_PUSH_NOTIFICATIONS=false
FEATURE_ANALYTICS=false
