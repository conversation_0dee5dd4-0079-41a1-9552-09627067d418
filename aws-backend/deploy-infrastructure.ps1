# GameFlex Infrastructure Deployment Script (PowerShell)
# This script deploys the AWS infrastructure using CloudFormation
# Supports both LocalStack (development) and real AWS (qa, staging, production)

param(
    [string]$Environment = "development",
    [string]$ProjectName = "gameflex",
    [string]$Region = "us-east-1",
    [string]$AwsProfile = "default",
    [switch]$Force,
    [switch]$Verbose,
    [switch]$DryRun
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Environment-specific configuration
$IsLocalStack = $Environment -eq "development"
$StackName = "$ProjectName-infrastructure-$Environment"

if ($IsLocalStack) {
    # LocalStack configuration
    $env:AWS_ACCESS_KEY_ID = "test"
    $env:AWS_SECRET_ACCESS_KEY = "test"
    $env:AWS_DEFAULT_REGION = $Region
    $ENDPOINT_URL = "http://localhost:45660"
    $TemplateFile = "cloudformation\gameflex-infrastructure.yaml"
}
else {
    # Real AWS configuration
    $ENDPOINT_URL = ""
    $TemplateFile = "cloudformation\gameflex-infrastructure.yaml"
}

# Parameters file
$ParametersFile = "cloudformation\parameters\$Environment.json"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[DEPLOY] $Message" -ForegroundColor Blue
}

# Test AWS CLI and connectivity
function Test-AwsConnection {
    try {
        if ($IsLocalStack) {
            $result = aws --endpoint-url=$ENDPOINT_URL sts get-caller-identity 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "AWS CLI connection to LocalStack successful"
                return $true
            }
            else {
                Write-Error "Failed to connect to LocalStack. Make sure LocalStack is running."
                return $false
            }
        }
        else {
            $result = aws sts get-caller-identity --profile $AwsProfile --region $Region 2>$null
            if ($LASTEXITCODE -eq 0) {
                $identity = $result | ConvertFrom-Json
                Write-Status "AWS CLI connection successful"
                Write-Status "AWS Identity: $($identity.Arn)"
                Write-Status "Account ID: $($identity.Account)"
                return $true
            }
            else {
                Write-Error "Failed to connect to AWS. Please check your credentials and profile."
                return $false
            }
        }
    }
    catch {
        Write-Error "AWS CLI not available or connection failed: $_"
        return $false
    }
}

# Package Lambda function - simplified version
function New-LambdaPackage {
    param([string]$FunctionPath, [string]$OutputPath)

    Write-Status "Packaging Lambda function: $FunctionPath"

    try {
        $functionName = Split-Path $FunctionPath -Leaf
        $zipPath = Join-Path $OutputPath "$functionName.zip"

        # Remove existing zip file
        if (Test-Path $zipPath) {
            Remove-Item $zipPath -Force
        }

        # Check if this is a TypeScript function
        $packageJsonPath = Join-Path $FunctionPath "package.json"
        if (Test-Path $packageJsonPath) {
            Write-Status "Building TypeScript Lambda function..."

            # Change to function directory
            Push-Location $FunctionPath
            try {
                # Install dependencies
                Write-Status "Installing dependencies for $functionName..."
                $null = & npm install --production=false --silent

                # Build TypeScript
                Write-Status "Building TypeScript for $functionName..."
                $null = & npm run build --silent
                if ($LASTEXITCODE -ne 0) {
                    Write-Warning "TypeScript build failed for $functionName, but continuing..."
                }

                # Install production dependencies only
                Write-Status "Installing production dependencies for $functionName..."
                $null = & npm ci --production --silent

                # Create temporary directory for packaging
                $tempDir = Join-Path $env:TEMP "lambda-$functionName-$(Get-Random)"
                New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

                # Copy files to temp directory
                $hasFiles = $false
                if (Test-Path "dist") {
                    Copy-Item -Path "dist\*" -Destination $tempDir -Recurse -Force
                    $hasFiles = $true
                }
                if (Test-Path "node_modules") {
                    Copy-Item -Path "node_modules" -Destination $tempDir -Recurse -Force
                    $hasFiles = $true
                }
                if (Test-Path "package.json") {
                    Copy-Item -Path "package.json" -Destination $tempDir -Force
                    $hasFiles = $true
                }

                # Create ZIP file
                if ($hasFiles) {
                    Compress-Archive -Path "$tempDir\*" -DestinationPath $zipPath -Force
                    Write-Status "Created Lambda package: $zipPath"
                }
                else {
                    Write-Warning "No files found to package for Lambda function $functionName"
                    return $null
                }

                # Clean up temp directory
                if (Test-Path $tempDir) {
                    Remove-Item $tempDir -Recurse -Force
                }

            }
            finally {
                Pop-Location
            }
        }
        else {
            Write-Warning "No package.json found for $functionName - skipping"
            return $null
        }

        return $zipPath
    }
    catch {
        Write-Error "Failed to package Lambda function: $_"
        return $null
    }
}

# Deploy CloudFormation stack
function Deploy-CloudFormationStack {
    param(
        [string]$StackName,
        [string]$TemplateFile,
        [string]$ParametersFile = ""
    )

    Write-Status "Deploying CloudFormation stack: $StackName"
    Write-Status "Using template: $TemplateFile"
    if ($ParametersFile) {
        Write-Status "Using parameters file: $ParametersFile"
    }

    try {
        # Check if stack exists
        $stackExists = $false
        try {
            if ($IsLocalStack) {
                $stackInfo = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name $StackName 2>$null
            }
            else {
                $stackInfo = aws cloudformation describe-stacks --stack-name $StackName --profile $AwsProfile --region $Region 2>$null
            }

            if ($LASTEXITCODE -eq 0 -and $stackInfo) {
                $stackData = $stackInfo | ConvertFrom-Json
                $stackStatus = $stackData.Stacks[0].StackStatus
                
                if ($stackStatus -match "ROLLBACK_COMPLETE|CREATE_FAILED|UPDATE_ROLLBACK_COMPLETE") {
                    Write-Warning "Stack $StackName is in a failed state ($stackStatus). Deleting and recreating..."
                    
                    # Delete the failed stack
                    if ($IsLocalStack) {
                        aws --endpoint-url=$ENDPOINT_URL cloudformation delete-stack --stack-name $StackName | Out-Null
                    }
                    else {
                        aws cloudformation delete-stack --stack-name $StackName --profile $AwsProfile --region $Region | Out-Null
                    }
                    
                    # Wait for deletion to complete
                    Write-Status "Waiting for stack deletion to complete..."
                    do {
                        Start-Sleep -Seconds 10
                        if ($IsLocalStack) {
                            $deleteStatus = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name $StackName 2>$null
                        }
                        else {
                            $deleteStatus = aws cloudformation describe-stacks --stack-name $StackName --profile $AwsProfile --region $Region 2>$null
                        }
                    } while ($LASTEXITCODE -eq 0)
                    
                    Write-Status "Stack deleted successfully. Will create new stack."
                    $stackExists = $false
                }
                else {
                    $stackExists = $true
                    Write-Status "Stack $StackName already exists (Status: $stackStatus), updating..."
                }
            }
        }
        catch {
            Write-Status "Stack $StackName does not exist, creating..."
        }

        # Prepare deployment command
        if ($IsLocalStack) {
            $baseCommand = "aws --endpoint-url=$ENDPOINT_URL cloudformation"
        }
        else {
            $baseCommand = "aws cloudformation --profile $AwsProfile --region $Region"
        }

        # Use change sets for better update handling
        if ($stackExists) {
            $changeSetName = "$StackName-changeset-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            Write-Status "Creating change set: $changeSetName"
            
            $command = "$baseCommand create-change-set --stack-name $StackName --change-set-name $changeSetName --template-body file://$TemplateFile --capabilities CAPABILITY_NAMED_IAM"
            
            # Add parameters file if provided
            if ($ParametersFile) {
                $command += " --parameters file://$ParametersFile"
            }
            
            # Add tags
            $command += " --tags Key=Environment,Value=$Environment Key=Project,Value=$ProjectName"
            
            if ($DryRun) {
                Write-Status "DRY RUN - Would execute: $command"
                return $true
            }
            
            # Create change set
            Write-Status "Executing: $command"
            Invoke-Expression $command | Out-Null
            
            if ($LASTEXITCODE -ne 0) {
                throw "Failed to create change set"
            }
            
            # Wait for change set creation
            Write-Status "Waiting for change set creation..."
            do {
                Start-Sleep -Seconds 5
                if ($IsLocalStack) {
                    $changeSetStatus = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-change-set --stack-name $StackName --change-set-name $changeSetName --query "Status" --output text 2>$null
                }
                else {
                    $changeSetStatus = aws cloudformation describe-change-set --stack-name $StackName --change-set-name $changeSetName --profile $AwsProfile --region $Region --query "Status" --output text 2>$null
                }
            } while ($changeSetStatus -eq "CREATE_IN_PROGRESS")
            
            if ($changeSetStatus -eq "FAILED") {
                Write-Warning "Change set creation failed - no changes detected or there are issues. Checking reason..."
                if ($IsLocalStack) {
                    $reason = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-change-set --stack-name $StackName --change-set-name $changeSetName --query "StatusReason" --output text 2>$null
                }
                else {
                    $reason = aws cloudformation describe-change-set --stack-name $StackName --change-set-name $changeSetName --profile $AwsProfile --region $Region --query "StatusReason" --output text 2>$null
                }
                
                if ($reason -match "No updates are to be performed") {
                    Write-Status "No changes detected in the stack. Stack is already up to date."
                    return $true
                }
                else {
                    throw "Change set failed: $reason"
                }
            }
            
            # Execute change set
            Write-Status "Executing change set..."
            if ($IsLocalStack) {
                aws --endpoint-url=$ENDPOINT_URL cloudformation execute-change-set --stack-name $StackName --change-set-name $changeSetName | Out-Null
            }
            else {
                aws cloudformation execute-change-set --stack-name $StackName --change-set-name $changeSetName --profile $AwsProfile --region $Region | Out-Null
            }
            
            if ($LASTEXITCODE -ne 0) {
                throw "Failed to execute change set"
            }
        }
        else {
            $command = "$baseCommand create-stack --stack-name $StackName --template-body file://$TemplateFile --capabilities CAPABILITY_NAMED_IAM"

            # Add termination protection for production
            if ($Environment -eq "production" -and -not $IsLocalStack) {
                $command += " --enable-termination-protection"
            }
            
            # Add parameters file if provided
            if ($ParametersFile) {
                $command += " --parameters file://$ParametersFile"
            }

            # Add tags
            $command += " --tags Key=Environment,Value=$Environment Key=Project,Value=$ProjectName"

            if ($DryRun) {
                Write-Status "DRY RUN - Would execute: $command"
                return $true
            }

            # Execute deployment
            Write-Status "Executing: $command"
            Invoke-Expression $command | Out-Null

            if ($LASTEXITCODE -ne 0) {
                throw "CloudFormation deployment failed"
            }
        }

        # Wait for stack to complete
        Write-Status "Waiting for stack deployment to complete..."
        $timeout = if ($IsLocalStack) { 300 } else { 1800 }  # 5 minutes for LocalStack, 30 minutes for real AWS
        $counter = 0

        do {
            Start-Sleep -Seconds 10
            $counter += 10

            if ($IsLocalStack) {
                $stackStatus = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].StackStatus" --output text 2>$null
            }
            else {
                $stackStatus = aws cloudformation describe-stacks --stack-name $StackName --profile $AwsProfile --region $Region --query "Stacks[0].StackStatus" --output text 2>$null
            }

            Write-Status "Stack status: $stackStatus"

            if ($stackStatus -match "COMPLETE") {
                Write-Status "Stack deployment completed with status: $stackStatus"
                return $true
            }
            elseif ($stackStatus -match "FAILED" -or $stackStatus -match "ROLLBACK") {
                Write-Error "Stack deployment failed with status: $stackStatus"

                # Get stack events for debugging
                Write-Status "Recent stack events:"
                if ($IsLocalStack) {
                    aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stack-events --stack-name $StackName --query "StackEvents[?ResourceStatus=='CREATE_FAILED' || ResourceStatus=='UPDATE_FAILED'].{Resource:LogicalResourceId,Status:ResourceStatus,Reason:ResourceStatusReason}" --output table
                }
                else {
                    aws cloudformation describe-stack-events --stack-name $StackName --profile $AwsProfile --region $Region --query "StackEvents[?ResourceStatus=='CREATE_FAILED' || ResourceStatus=='UPDATE_FAILED'].{Resource:LogicalResourceId,Status:ResourceStatus,Reason:ResourceStatusReason}" --output table
                }

                return $false
            }

            if ($counter -ge $timeout) {
                Write-Error "Stack deployment timeout"
                return $false
            }

        } while ($true)
    }
    catch {
        Write-Error "Failed to deploy CloudFormation stack: $_"
        return $false
    }
}

# Update Lambda function code
function Update-LambdaFunction {
    param(
        [string]$FunctionName,
        [string]$ZipFilePath
    )

    Write-Status "Updating Lambda function: $FunctionName"

    try {
        if ($IsLocalStack) {
            aws --endpoint-url=$ENDPOINT_URL lambda update-function-code --function-name $FunctionName --zip-file fileb://$ZipFilePath | Out-Null
        }
        else {
            aws lambda update-function-code --function-name $FunctionName --zip-file fileb://$ZipFilePath --profile $AwsProfile --region $Region | Out-Null
        }

        if ($LASTEXITCODE -eq 0) {
            Write-Status "Lambda function updated successfully"
            return $true
        }
        else {
            Write-Error "Failed to update Lambda function"
            return $false
        }
    }
    catch {
        Write-Error "Failed to update Lambda function: $_"
        return $false
    }
}

# Get stack outputs
function Get-StackOutputs {
    param([string]$StackName)

    try {
        if ($IsLocalStack) {
            $outputs = aws --endpoint-url=$ENDPOINT_URL cloudformation describe-stacks --stack-name $StackName --query "Stacks[0].Outputs" --output json | ConvertFrom-Json
        }
        else {
            $outputs = aws cloudformation describe-stacks --stack-name $StackName --profile $AwsProfile --region $Region --query "Stacks[0].Outputs" --output json | ConvertFrom-Json
        }

        $outputHash = @{}
        foreach ($output in $outputs) {
            $outputHash[$output.OutputKey] = $output.OutputValue
        }

        return $outputHash
    }
    catch {
        Write-Warning "Failed to get stack outputs: $_"
        return @{}
    }
}

# Main deployment function
function Deploy-Infrastructure {
    Write-Header "Deploying GameFlex AWS Infrastructure"
    Write-Host ""
    
    # Test connection
    if (-not (Test-AwsConnection)) {
        Write-Error "Cannot connect to LocalStack. Make sure it's running."
        exit 1
    }
    
    # Create packages directory
    $packagesDir = "packages"
    if (-not (Test-Path $packagesDir)) {
        New-Item -ItemType Directory -Path $packagesDir | Out-Null
    }
    
    # Use pre-built Lambda packages
    Write-Status "Using pre-built Lambda packages..."
    $usersPackage = "$packagesDir\users.zip"

    if (-not (Test-Path $usersPackage)) {
        Write-Error "Pre-built Users Lambda package not found at: $usersPackage"
        Write-Error "Please run: cd lambda-functions\users && npm install && npm run build && npm ci --production && Compress-Archive -Path 'dist\*', 'node_modules', 'package.json' -DestinationPath '..\..\packages\users.zip' -Force"
        exit 1
    }

    Write-Status "Found Users Lambda package: $usersPackage"

    # Skip other functions for now since they have TypeScript compilation errors
    Write-Warning "Skipping Auth, Posts, and Media Lambda functions due to compilation errors"
    
    # Validate parameters file exists
    if (-not (Test-Path $ParametersFile)) {
        Write-Error "Parameters file not found: $ParametersFile"
        exit 1
    }

    # Deploy CloudFormation stack
    $deployResult = Deploy-CloudFormationStack -StackName $StackName -TemplateFile $TemplateFile -ParametersFile $ParametersFile
    
    if (-not $deployResult) {
        Write-Error "Failed to deploy CloudFormation stack"
        exit 1
    }
    
    # Update Lambda function code - only users function for now
    $usersFunctionName = "$ProjectName-users-$Environment"

    # Update users function
    $usersUpdateResult = Update-LambdaFunction -FunctionName $usersFunctionName -ZipFilePath $usersPackage

    if (-not $usersUpdateResult) {
        Write-Warning "Failed to update Users Lambda function, but continuing..."
    }
    else {
        Write-Status "Successfully updated Users Lambda function"
    }
    
    # Get stack outputs
    $outputs = Get-StackOutputs -StackName $stackName
    
    # Display deployment information
    Write-Host ""
    Write-Header "Deployment completed successfully!"
    Write-Host ""
    
    Write-Status "Stack Outputs:"
    foreach ($key in $outputs.Keys) {
        Write-Host "  $key : $($outputs[$key])" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Status "Next Steps:"
    Write-Host "  1. Update your .env file with the Cognito IDs" -ForegroundColor Cyan
    Write-Host "  2. Test the API endpoints" -ForegroundColor Cyan
    Write-Host "  3. Deploy additional Lambda functions as needed" -ForegroundColor Cyan
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex Infrastructure Deployment Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\deploy-infrastructure.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Environment    Environment name (default: development)"
    Write-Host "  -ProjectName    Project name (default: gameflex)"
    Write-Host "  -Force          Force deployment even if validation fails"
    Write-Host "  -Verbose        Show verbose output"
    Write-Host "  -h, --help      Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\deploy-infrastructure.ps1"
    Write-Host "  .\deploy-infrastructure.ps1 -Environment staging"
    Write-Host "  .\deploy-infrastructure.ps1 -ProjectName mygame -Environment production"
    exit 0
}

# Run deployment
try {
    Deploy-Infrastructure
}
catch {
    Write-Error "Deployment failed: $_"
    exit 1
}
