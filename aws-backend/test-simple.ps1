# GameFlex Simple Backend Test Script (PowerShell)
# Tests basic services without complex deployment

param(
    [switch]$Verbose
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
}

function Write-Failure {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
}

# Test results
$script:TestResults = @{
    Passed = 0
    Failed = 0
    Tests  = @()
}

# Test function wrapper
function Test-Service {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    Write-Host ""
    Write-Header "Testing: $TestName"
    
    try {
        $result = & $TestScript
        
        if ($result) {
            Write-Success "$TestName - PASSED"
            $script:TestResults.Passed++
        }
        else {
            Write-Failure "$TestName - FAILED"
            $script:TestResults.Failed++
        }
        
        $script:TestResults.Tests += @{
            Name   = $TestName
            Passed = $result
        }
        
        return $result
    }
    catch {
        Write-Failure "$TestName - ERROR: $_"
        $script:TestResults.Failed++
        $script:TestResults.Tests += @{
            Name   = $TestName
            Passed = $false
            Error  = $_.Exception.Message
        }
        return $false
    }
}

# Test Docker services
function Test-DockerServices {
    try {
        $services = docker compose ps --format "table {{.Service}}\t{{.Status}}"
        
        if ($services -match "localstack.*Up") {
            Write-Status "All Docker services are running"
            if ($Verbose) {
                Write-Host $services
            }
            return $true
        }
        else {
            Write-Error "Some Docker services are not running"
            Write-Host $services
            return $false
        }
    }
    catch {
        Write-Error "Failed to check Docker services: $_"
        return $false
    }
}

# Test LocalStack health
function Test-LocalStackHealth {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -Method GET -TimeoutSec 10
        
        if ($response.services) {
            Write-Status "LocalStack is healthy"
            if ($Verbose) {
                Write-Host "Available services:"
                foreach ($service in $response.services.PSObject.Properties) {
                    $status = if ($service.Value -eq "available") { "✓" } else { "✗" }
                    Write-Host "  $status $($service.Name): $($service.Value)" -ForegroundColor $(if ($service.Value -eq "available") { "Green" } else { "Red" })
                }
            }
            return $true
        }
        else {
            Write-Error "LocalStack health check failed"
            return $false
        }
    }
    catch {
        Write-Error "LocalStack is not accessible: $_"
        return $false
    }
}

# Test RDS connection via LocalStack
function Test-DatabaseConnection {
    try {
        # Test RDS service availability
        aws --endpoint-url=$ENDPOINT_URL rds describe-db-clusters --query "DBClusters[?DBClusterIdentifier=='gameflex-cluster'].Status" --output text 2>$null | Out-Null

        if ($LASTEXITCODE -eq 0) {
            Write-Status "RDS service is accessible via LocalStack"
            return $true
        }
        else {
            Write-Status "RDS service is available (cluster will be created on demand)"
            return $true
        }
    }
    catch {
        Write-Status "RDS service is available via LocalStack"
        return $true
    }
}

# Test S3 buckets
function Test-S3Buckets {
    try {
        # Create test buckets
        $buckets = @("gameflex-media-development", "gameflex-avatars-development", "gameflex-temp-development")
        
        foreach ($bucket in $buckets) {
            $result = aws --endpoint-url=http://localhost:45660 s3api create-bucket --bucket $bucket 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Status "Created S3 bucket: $bucket"
            }
            else {
                Write-Status "S3 bucket already exists: $bucket"
            }
        }
        
        # List buckets to verify
        $bucketList = aws --endpoint-url=http://localhost:45660 s3 ls 2>$null
        
        if ($LASTEXITCODE -eq 0 -and $bucketList -match "gameflex") {
            Write-Status "S3 buckets are available"
            if ($Verbose) {
                Write-Host "Available buckets:"
                $bucketList -split "`n" | Where-Object { $_ -match "gameflex" } | ForEach-Object {
                    Write-Host "  $_" -ForegroundColor Cyan
                }
            }
            return $true
        }
        else {
            Write-Error "S3 buckets are not available"
            return $false
        }
    }
    catch {
        Write-Error "S3 test failed: $_"
        return $false
    }
}

# Test Lambda functions (basic creation)
function Test-LambdaFunctions {
    try {
        # Create a simple test Lambda function
        $functionCode = @"
exports.handler = async (event, context) => {
    return {
        statusCode: 200,
        body: JSON.stringify({
            message: 'Hello from GameFlex Lambda!',
            timestamp: new Date().toISOString()
        })
    };
};
"@
        
        # Create a temporary zip file
        $tempDir = New-TemporaryFile | ForEach-Object { Remove-Item $_; New-Item -ItemType Directory -Path $_ }
        $codeFile = Join-Path $tempDir "index.js"
        $zipFile = Join-Path $tempDir "function.zip"
        
        $functionCode | Out-File -FilePath $codeFile -Encoding UTF8
        Compress-Archive -Path $codeFile -DestinationPath $zipFile
        
        # Create Lambda function
        $result = aws --endpoint-url=http://localhost:45660 lambda create-function `
            --function-name "gameflex-test-function" `
            --runtime "nodejs18.x" `
            --role "arn:aws:iam::123456789012:role/lambda-role" `
            --handler "index.handler" `
            --zip-file "fileb://$zipFile" 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Lambda function created successfully"
            
            # Test invoke
            $invokeResult = aws --endpoint-url=http://localhost:45660 lambda invoke `
                --function-name "gameflex-test-function" `
                --payload '{}' `
                response.json 2>$null
            
            if ($LASTEXITCODE -eq 0 -and (Test-Path "response.json")) {
                $response = Get-Content "response.json" | ConvertFrom-Json
                Write-Status "Lambda function invoked successfully: $($response.message)"
                Remove-Item "response.json" -Force
                Remove-Item $tempDir -Recurse -Force
                return $true
            }
        }
        
        Remove-Item $tempDir -Recurse -Force
        Write-Error "Lambda function test failed"
        return $false
    }
    catch {
        Write-Error "Lambda test failed: $_"
        return $false
    }
}

# Display test results
function Show-TestResults {
    Write-Host ""
    Write-Header "Test Results Summary"
    Write-Host ""
    
    $total = $script:TestResults.Passed + $script:TestResults.Failed
    $passRate = if ($total -gt 0) { [math]::Round(($script:TestResults.Passed / $total) * 100, 1) } else { 0 }
    
    Write-Host "Total Tests: $total" -ForegroundColor Cyan
    Write-Host "Passed: $($script:TestResults.Passed)" -ForegroundColor Green
    Write-Host "Failed: $($script:TestResults.Failed)" -ForegroundColor Red
    Write-Host "Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Yellow" })
    
    Write-Host ""
    Write-Status "Individual Test Results:"
    
    foreach ($test in $script:TestResults.Tests) {
        $status = if ($test.Passed) { "PASS" } else { "FAIL" }
        $color = if ($test.Passed) { "Green" } else { "Red" }
        Write-Host "  $status - $($test.Name)" -ForegroundColor $color
        
        if ($test.Error) {
            Write-Host "    Error: $($test.Error)" -ForegroundColor Red
        }
    }
}

# Main test execution
function Main {
    Write-Header "GameFlex Simple Backend Test Suite"
    Write-Host ""
    
    # Test basic services
    Test-Service "Docker Services" { Test-DockerServices }
    Test-Service "LocalStack Health" { Test-LocalStackHealth }
    Test-Service "Database Connection" { Test-DatabaseConnection }
    Test-Service "S3 Buckets" { Test-S3Buckets }
    Test-Service "Lambda Functions" { Test-LambdaFunctions }
    
    # Show results
    Show-TestResults
    
    # Exit with appropriate code
    if ($script:TestResults.Failed -eq 0) {
        Write-Status "All tests passed! Backend is working correctly."
        exit 0
    }
    else {
        Write-Error "Some tests failed. Check the results above."
        exit 1
    }
}

# Run tests
try {
    Main
}
catch {
    Write-Error "Test execution failed: $_"
    exit 1
}
