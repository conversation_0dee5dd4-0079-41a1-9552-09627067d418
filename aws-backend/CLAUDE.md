## Project
- This is a tik tok like app for gamers.

## Environment
- Ubuntu Linux, Docker, AWS SAM CLI, Bash

## Backend
- aws-backend folder, AWS SAM CLI for Development
- jest testing
- Cloudformation to set up all the services

## Fixes
- Fixes should be permanent and you shouldn't rely on temporary fixes

## Testing
- Always use aws-backend/start.sh and aws-backend/stop.sh when starting or stopping backend services
- DO NOT create md files for fixes, features, or changes