# GameFlex AWS Backend Health Check Script (PowerShell)
# Quick health check for all backend services

param(
    [switch]$Detailed,
    [switch]$Quiet
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    if (-not $Quiet) { Write-Host "[INFO] $Message" -ForegroundColor Green }
}

function Write-Warning {
    param([string]$Message)
    if (-not $Quiet) { Write-Host "[WARN] $Message" -ForegroundColor Yellow }
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    if (-not $Quiet) { Write-Host "[HEALTH] $Message" -ForegroundColor Blue }
}

# Health check results
$script:HealthStatus = @{
    Docker     = $false
    LocalStack = $false
    RDS        = $false
    S3         = $false
    Cognito    = $false
    ApiGateway = $false
}

# Check Docker
function Test-Docker {
    try {
        docker info | Out-Null 2>&1
        if ($LASTEXITCODE -eq 0) {
            $script:HealthStatus.Docker = $true
            Write-Status "✓ Docker is running"
            return $true
        }
    }
    catch { }
    
    Write-Error "✗ Docker is not running"
    return $false
}

# Check LocalStack
function Test-LocalStack {
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.services) {
            $script:HealthStatus.LocalStack = $true
            Write-Status "✓ LocalStack is healthy"
            
            if ($Detailed) {
                foreach ($service in $response.services.PSObject.Properties) {
                    $status = if ($service.Value -eq "available") { "✓" } else { "✗" }
                    Write-Host "  $status $($service.Name): $($service.Value)" -ForegroundColor $(if ($service.Value -eq "available") { "Green" } else { "Red" })
                }
            }
            return $true
        }
    }
    catch { }
    
    Write-Error "✗ LocalStack is not accessible"
    return $false
}

# Check RDS via LocalStack
function Test-RDS {
    try {
        # Test RDS service availability
        aws --endpoint-url=http://localhost:45660 rds describe-db-clusters --query "DBClusters[?DBClusterIdentifier=='gameflex-cluster'].Status" --output text 2>$null | Out-Null

        if ($LASTEXITCODE -eq 0) {
            $script:HealthStatus.RDS = $true
            Write-Status "✓ RDS service is accessible via LocalStack"

            if ($Detailed) {
                $clusters = aws --endpoint-url=http://localhost:45660 rds describe-db-clusters --query "DBClusters[?DBClusterIdentifier=='gameflex-cluster'].{Status:Status,Engine:Engine}" --output table 2>$null
                if ($LASTEXITCODE -eq 0 -and $clusters) {
                    Write-Host "  RDS Cluster Details:" -ForegroundColor Cyan
                    Write-Host $clusters -ForegroundColor Gray
                }
            }
            return $true
        }
        else {
            # RDS service is available but no clusters yet
            $script:HealthStatus.RDS = $true
            Write-Status "✓ RDS service is available (no clusters created yet)"
            return $true
        }
    }
    catch { }

    Write-Error "✗ RDS service is not accessible"
    return $false
}

# Check S3 buckets
function Test-S3 {
    try {
        $buckets = aws --endpoint-url=http://localhost:45660 s3 ls 2>$null
        if ($LASTEXITCODE -eq 0 -and $buckets -match "gameflex") {
            $script:HealthStatus.S3 = $true
            Write-Status "✓ S3 buckets are available"
            
            if ($Detailed) {
                $bucketList = $buckets -split "`n" | Where-Object { $_ -match "gameflex" }
                foreach ($bucket in $bucketList) {
                    Write-Host "  $bucket" -ForegroundColor Cyan
                }
            }
            return $true
        }
    }
    catch { }
    
    Write-Error "✗ S3 buckets are not available"
    return $false
}

# Check Cognito
function Test-Cognito {
    try {
        $userPools = aws --endpoint-url=http://localhost:45660 cognito-idp list-user-pools --max-results 10 2>$null
        if ($LASTEXITCODE -eq 0 -and $userPools -match "gameflex") {
            $script:HealthStatus.Cognito = $true
            Write-Status "✓ Cognito User Pools are available"
            return $true
        }
    }
    catch { }
    
    Write-Error "✗ Cognito User Pools are not available"
    return $false
}

# Check API Gateway
function Test-ApiGateway {
    try {
        $apis = aws --endpoint-url=http://localhost:45660 apigateway get-rest-apis 2>$null
        if ($LASTEXITCODE -eq 0 -and $apis -match "gameflex") {
            $script:HealthStatus.ApiGateway = $true
            Write-Status "✓ API Gateway is available"
            
            if ($Detailed) {
                $apiData = $apis | ConvertFrom-Json
                foreach ($api in $apiData.items) {
                    if ($api.name -match "gameflex") {
                        Write-Host "  API: $($api.name) (ID: $($api.id))" -ForegroundColor Cyan
                    }
                }
            }
            return $true
        }
    }
    catch { }
    
    Write-Error "✗ API Gateway is not available"
    return $false
}

# Main health check
function Main {
    if (-not $Quiet) {
        Write-Header "GameFlex AWS Backend Health Check"
        Write-Host ""
    }
    
    # Run all health checks
    Test-Docker
    Test-LocalStack
    Test-RDS
    Test-S3
    Test-Cognito
    Test-ApiGateway
    
    # Calculate overall health
    $totalServices = $script:HealthStatus.Count
    $healthyServices = ($script:HealthStatus.Values | Where-Object { $_ -eq $true }).Count
    $healthPercentage = [math]::Round(($healthyServices / $totalServices) * 100, 1)
    
    if (-not $Quiet) {
        Write-Host ""
        Write-Header "Health Summary"
        Write-Host "Healthy Services: $healthyServices/$totalServices ($healthPercentage%)" -ForegroundColor $(
            if ($healthPercentage -eq 100) { "Green" }
            elseif ($healthPercentage -ge 80) { "Yellow" }
            else { "Red" }
        )
    }
    
    # Exit with appropriate code
    if ($healthyServices -eq $totalServices) {
        if (-not $Quiet) { Write-Status "All services are healthy!" }
        exit 0
    }
    else {
        Write-Error "Some services are unhealthy"
        exit 1
    }
}

# Show help if requested
if ($args -contains "-h" -or $args -contains "--help") {
    Write-Host "GameFlex AWS Backend Health Check Script" -ForegroundColor Blue
    Write-Host ""
    Write-Host "Usage: .\health-check.ps1 [OPTIONS]" -ForegroundColor Green
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  -Detailed     Show detailed information for each service"
    Write-Host "  -Quiet        Suppress informational output (errors only)"
    Write-Host "  -h, --help    Show this help message"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\health-check.ps1"
    Write-Host "  .\health-check.ps1 -Detailed"
    Write-Host "  .\health-check.ps1 -Quiet"
    Write-Host ""
    Write-Host "Exit Codes:" -ForegroundColor Yellow
    Write-Host "  0 - All services healthy"
    Write-Host "  1 - One or more services unhealthy"
    exit 0
}

# Run health check
try {
    Main
}
catch {
    Write-Error "Health check failed: $_"
    exit 1
}
