#!/bin/bash

# GameFlex AWS Backend Startup Script
# This script starts the AWS backend using LocalStack Pro

set -e

# Load environment variables from .env file
load_env_file() {
    if [ -f ".env" ]; then
        print_status "Loading environment variables from .env file..."
        # Export variables from .env file, ignoring comments and empty lines
        export $(grep -v '^#' .env | grep -v '^$' | xargs)
    else
        print_warning ".env file not found. Using default values."
    fi
}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[GAMEFLEX]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_status "Docker is running"
}

# Check if required ports are available
check_ports() {
    local ports=("4566" "5433" "6380")
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            print_warning "Port $port is already in use"
            read -p "Do you want to continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                print_error "Startup cancelled"
                exit 1
            fi
        fi
    done
    print_status "Port check completed"
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p init
    mkdir -p lambda-functions
    mkdir -p cloudformation
    mkdir -p database/init
    mkdir -p logs
    print_status "Directories created"
}

# Start services
start_services() {
    print_header "Starting GameFlex AWS Backend..."
    
    # Pull latest images
    print_status "Pulling latest Docker images..."
    docker-compose pull
    
    # Start services
    print_status "Starting services..."
    docker-compose up -d
    
    # Wait for services to be healthy
    print_status "Waiting for services to be ready..."
    
    # Wait for LocalStack
    print_status "Waiting for LocalStack to be ready..."
    timeout=120
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -s http://localhost:4566/_localstack/health > /dev/null 2>&1; then
            print_status "LocalStack is ready"
            break
        fi
        sleep 2
        counter=$((counter + 2))
        if [ $counter -ge $timeout ]; then
            print_error "LocalStack failed to start within $timeout seconds"
            docker-compose logs localstack
            exit 1
        fi
    done
    
    # Wait for PostgreSQL
    print_status "Waiting for PostgreSQL to be ready..."
    timeout=60
    counter=0
    while [ $counter -lt $timeout ]; do
        if docker-compose exec -T postgres pg_isready -U postgres -d gameflex > /dev/null 2>&1; then
            print_status "PostgreSQL is ready"
            break
        fi
        sleep 2
        counter=$((counter + 2))
        if [ $counter -ge $timeout ]; then
            print_error "PostgreSQL failed to start within $timeout seconds"
            docker-compose logs postgres
            exit 1
        fi
    done
    
    # Wait for Redis
    print_status "Waiting for Redis to be ready..."
    timeout=30
    counter=0
    while [ $counter -lt $timeout ]; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            print_status "Redis is ready"
            break
        fi
        sleep 1
        counter=$((counter + 1))
        if [ $counter -ge $timeout ]; then
            print_error "Redis failed to start within $timeout seconds"
            docker-compose logs redis
            exit 1
        fi
    done
}

# Initialize AWS services
initialize_aws_services() {
    print_status "Initializing AWS services..."

    # Check if shell initialization script exists (preferred)
    if [ -f "init/init-aws-services.sh" ]; then
        print_status "Running AWS services initialization (shell)..."
        chmod +x init/init-aws-services.sh
        ./init/init-aws-services.sh
    elif [ -f "init/init-aws-services.ps1" ]; then
        print_status "Running AWS services initialization (PowerShell)..."
        powershell.exe -ExecutionPolicy Bypass -File "init/init-aws-services.ps1"
    else
        print_warning "AWS services initialization script not found"
        print_warning "You may need to run the initialization manually"
    fi
}

# Display service information
display_info() {
    print_header "GameFlex AWS Backend is now running!"
    echo
    print_status "Service URLs:"
    echo "  🌐 LocalStack Dashboard: http://localhost:4566/_localstack/health"
    echo "  🗄️  PostgreSQL: localhost:5433 (user: postgres, db: gameflex)"
    echo "  🔴 Redis: localhost:6380"
    echo "  📦 S3 Console: http://localhost:4566/_aws/s3"
    echo "  🔐 Cognito Console: http://localhost:4566/_aws/cognito"
    echo
    print_status "Development Credentials:"
    echo "  📧 Developer: <EMAIL> / DevPassword123!"
    echo "  👑 Admin: <EMAIL> / AdminPassword123!"
    echo
    print_status "Useful Commands:"
    echo "  📊 Check status: docker-compose ps"
    echo "  📋 View logs: docker-compose logs -f"
    echo "  🛑 Stop services: ./stop.sh"
    echo "  🔄 Restart: ./stop.sh && ./start.sh"
    echo
    print_warning "This is a development environment. Do not use in production!"
}

# Main execution
main() {
    print_header "GameFlex AWS Backend Startup"
    echo

    # Load environment variables from .env file
    load_env_file

    check_docker
    check_ports
    create_directories
    start_services
    initialize_aws_services
    display_info
    
    print_status "Startup completed successfully!"
}

# Run main function
main "$@"
