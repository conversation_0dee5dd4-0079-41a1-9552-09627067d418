#!/bin/bash

# GameFlex DynamoDB Tables Creation Script
# This script creates all DynamoDB tables for the GameFlex application
# Runs automatically when LocalStack starts

set -e

echo "[DYNAMODB] Creating DynamoDB tables for GameFlex..."

# Function to create a DynamoDB table using awslocal
create_table() {
    local table_name=$1
    local attributes=$2
    local key_schema=$3
    local global_secondary_indexes=$4

    echo "[INFO] Creating table: $table_name"

    # Check if table exists
    if awslocal dynamodb describe-table --table-name "$table_name" >/dev/null 2>&1; then
        echo "[INFO] Table $table_name already exists"
        return 0
    fi

    # Create table
    if [ -n "$global_secondary_indexes" ]; then
        awslocal dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions $attributes \
            --key-schema $key_schema \
            --global-secondary-indexes "$global_secondary_indexes" \
            --billing-mode PAY_PER_REQUEST
    else
        awslocal dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions $attributes \
            --key-schema $key_schema \
            --billing-mode PAY_PER_REQUEST
    fi

    if [ $? -eq 0 ]; then
        echo "[INFO] Created DynamoDB table: $table_name"
        return 0
    else
        echo "[ERROR] Failed to create table $table_name"
        return 1
    fi
}

# Main execution
echo "[DYNAMODB] Starting table creation..."

# Create Users table with CognitoUserIdIndex
create_table "Users" \
    "AttributeName=id,AttributeType=S AttributeName=cognito_user_id,AttributeType=S" \
    "AttributeName=id,KeyType=HASH" \
    '[{"IndexName":"CognitoUserIdIndex","KeySchema":[{"AttributeName":"cognito_user_id","KeyType":"HASH"}],"Projection":{"ProjectionType":"ALL"},"ProvisionedThroughput":{"ReadCapacityUnits":5,"WriteCapacityUnits":5}}]'

# Create UserProfiles table
create_table "UserProfiles" "AttributeName=user_id,AttributeType=S" "AttributeName=user_id,KeyType=HASH"

# Create Channels table
create_table "Channels" "AttributeName=id,AttributeType=S" "AttributeName=id,KeyType=HASH"

# Create ChannelMembers table (with composite key)
create_table "ChannelMembers" "AttributeName=channel_id,AttributeType=S AttributeName=user_id,AttributeType=S" "AttributeName=channel_id,KeyType=HASH AttributeName=user_id,KeyType=RANGE"

# Create Media table
create_table "Media" "AttributeName=id,AttributeType=S" "AttributeName=id,KeyType=HASH"

# Create Posts table
create_table "Posts" "AttributeName=id,AttributeType=S" "AttributeName=id,KeyType=HASH"

# Create Comments table (with composite key)
create_table "Comments" "AttributeName=id,AttributeType=S AttributeName=post_id,AttributeType=S" "AttributeName=id,KeyType=HASH AttributeName=post_id,KeyType=RANGE"

# Create Likes table (with composite key)
create_table "Likes" "AttributeName=post_id,AttributeType=S AttributeName=user_id,AttributeType=S" "AttributeName=post_id,KeyType=HASH AttributeName=user_id,KeyType=RANGE"

# Create Follows table (with composite key)
create_table "Follows" "AttributeName=follower_id,AttributeType=S AttributeName=following_id,AttributeType=S" "AttributeName=follower_id,KeyType=HASH AttributeName=following_id,KeyType=RANGE"

# Create Notifications table (with composite key)
create_table "Notifications" "AttributeName=user_id,AttributeType=S AttributeName=created_at,AttributeType=S" "AttributeName=user_id,KeyType=HASH AttributeName=created_at,KeyType=RANGE"

echo "[DYNAMODB] All DynamoDB tables created successfully!"
