#!/bin/bash

# GameFlex Cognito Users Initialization Script
# This script creates Cognito users in LocalStack
# Runs automatically when LocalStack starts - FIRST initialization script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${BLUE}[COGNITO]${NC} $1"
}

log_header "Initializing Cognito users for GameFlex..."

# Function to create a Cognito user
create_cognito_user() {
    local email=$1
    local password=$2
    local display_name=$3
    local username=$4
    local user_pool_id=$5

    log_info "Creating Cognito user: $email"

    # Check if user already exists
    local existing_user=$(awslocal cognito-idp list-users \
        --user-pool-id "$user_pool_id" \
        --filter "email = \"$email\"" \
        --query 'Users[0].Username' \
        --output text 2>/dev/null || echo "None")

    if [ "$existing_user" != "None" ] && [ "$existing_user" != "" ]; then
        log_info "User $email already exists with username: $existing_user"
        return 0
    fi

    # Create the user
    local create_result=$(awslocal cognito-idp admin-create-user \
        --user-pool-id "$user_pool_id" \
        --username "$username" \
        --user-attributes \
            Name=email,Value="$email" \
            Name=email_verified,Value=true \
            Name=name,Value="$display_name" \
        --temporary-password "TempPass123!" \
        --message-action SUPPRESS \
        --query 'User.Username' \
        --output text)

    if [ $? -eq 0 ]; then
        echo "[INFO] Created user: $create_result"
        
        # Set permanent password
        awslocal cognito-idp admin-set-user-password \
            --user-pool-id "$user_pool_id" \
            --username "$username" \
            --password "$password" \
            --permanent

        echo "[INFO] Set permanent password for user: $username"
    else
        echo "[ERROR] Failed to create user: $email"
        return 1
    fi
}

# Set AWS environment variables for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1

# Wait for Cognito to be available
echo "[INFO] Waiting for Cognito to be available..."
timeout=60
counter=0
while [ $counter -lt $timeout ]; do
    if awslocal cognito-idp list-user-pools --max-results 1 > /dev/null 2>&1; then
        echo "[INFO] Cognito is available"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    if [ $counter -ge $timeout ]; then
        echo "[ERROR] Cognito not available within $timeout seconds"
        echo "[INFO] Attempting to continue anyway..."
        break
    fi
done

# Get the User Pool ID (assuming it's created by the infrastructure)
echo "[INFO] Looking for GameFlex User Pool..."
USER_POOL_ID=$(awslocal cognito-idp list-user-pools --max-results 50 \
    --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
    --output text | head -n1)

if [ -z "$USER_POOL_ID" ] || [ "$USER_POOL_ID" = "None" ]; then
    echo "[WARN] GameFlex User Pool not found. Attempting to create it..."
    
    # Create User Pool
    USER_POOL_RESULT=$(awslocal cognito-idp create-user-pool \
        --pool-name "GameFlex-Development" \
        --policies '{
            "PasswordPolicy": {
                "MinimumLength": 8,
                "RequireUppercase": false,
                "RequireLowercase": false,
                "RequireNumbers": false,
                "RequireSymbols": false
            }
        }' \
        --auto-verified-attributes email \
        --username-attributes email \
        --query 'UserPool.Id' \
        --output text)
    
    if [ $? -eq 0 ]; then
        USER_POOL_ID="$USER_POOL_RESULT"
        echo "[INFO] Created User Pool: $USER_POOL_ID"
        
        # Create User Pool Client
        CLIENT_RESULT=$(awslocal cognito-idp create-user-pool-client \
            --user-pool-id "$USER_POOL_ID" \
            --client-name "GameFlex-Mobile-Client" \
            --generate-secret \
            --explicit-auth-flows ADMIN_NO_SRP_AUTH USER_PASSWORD_AUTH \
            --query 'UserPoolClient.ClientId' \
            --output text)
        
        echo "[INFO] Created User Pool Client: $CLIENT_RESULT"
    else
        echo "[ERROR] Failed to create User Pool"
        exit 1
    fi
else
    echo "[INFO] Found User Pool: $USER_POOL_ID"
fi

# Create development users based on backend data
echo "[INFO] Creating development users..."

# Developer user (from backend seed data) - use email as username
create_cognito_user "<EMAIL>" "DevPassword123!" "GameFlex Developer" "<EMAIL>" "$USER_POOL_ID"

# Admin user - use email as username
create_cognito_user "<EMAIL>" "AdminPassword123!" "GameFlex Admin" "<EMAIL>" "$USER_POOL_ID"

# John Doe user (from backend seed data) - use email as username
create_cognito_user "<EMAIL>" "JohnPassword123!" "John Doe" "<EMAIL>" "$USER_POOL_ID"

# Jane Smith user (from backend seed data) - use email as username
create_cognito_user "<EMAIL>" "JanePassword123!" "Jane Smith" "<EMAIL>" "$USER_POOL_ID"

# Mike Wilson user (from backend seed data) - use email as username
create_cognito_user "<EMAIL>" "MikePassword123!" "Mike Wilson" "<EMAIL>" "$USER_POOL_ID"

# Alice user - use email as username
create_cognito_user "<EMAIL>" "AlicePassword123!" "Alice Cooper" "<EMAIL>" "$USER_POOL_ID"

# Bob user - use email as username
create_cognito_user "<EMAIL>" "BobPassword123!" "Bob Wilson" "<EMAIL>" "$USER_POOL_ID"

# Charlie user - use email as username
create_cognito_user "<EMAIL>" "CharliePassword123!" "Charlie Brown" "<EMAIL>" "$USER_POOL_ID"

echo "[COGNITO] Cognito users initialization completed successfully!"
echo "[INFO] User Pool ID: $USER_POOL_ID"
echo ""
echo "[INFO] Development users created:"
echo "  - <EMAIL> (password: DevPassword123!)"
echo "  - <EMAIL> (password: AdminPassword123!)"
echo "  - <EMAIL> (password: JohnPassword123!)"
echo "  - <EMAIL> (password: JanePassword123!)"
echo "  - <EMAIL> (password: MikePassword123!)"
echo "  - <EMAIL> (password: AlicePassword123!)"
echo "  - <EMAIL> (password: BobPassword123!)"
echo "  - <EMAIL> (password: CharliePassword123!)"
