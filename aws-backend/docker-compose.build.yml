version: '3.8'

services:
  deployer:
    build:
      context: ./docker
      dockerfile: deployer.Dockerfile
    volumes:
      - .:/workspace
      - ./docker/deploy.sh:/usr/local/bin/deploy.sh:ro
    environment:
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - PROJECT_NAME=${PROJECT_NAME:-gameflex}
      - AWS_ENDPOINT_URL=${AWS_ENDPOINT_URL:-http://host.docker.internal:45660}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-test}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-test}
      - AWS_DEFAULT_REGION=${AWS_DEFAULT_REGION:-us-east-1}
    extra_hosts:
      - "host.docker.internal:host-gateway"
    profiles:
      - deploy

networks:
  default:
    name: aws-backend_gameflex-aws
    external: true
