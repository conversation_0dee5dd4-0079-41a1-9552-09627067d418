# GameFlex AWS Backend Testing Framework

This document describes the comprehensive testing framework for the GameFlex AWS backend, built using Jest and LocalStack.

## Overview

The testing framework provides multiple levels of testing:

- **Unit Tests**: Test individual Lambda functions with mocked AWS services
- **Integration Tests**: Test AWS service interactions using LocalStack
- **API Tests**: Test complete HTTP request/response flows through API Gateway
- **End-to-End Tests**: Test complete user journeys across all services

## Prerequisites

1. **Node.js** (v18+) and npm
2. **LocalStack** running on port 45660
3. **PowerShell** (for Windows scripts)

## Quick Start

1. **Start LocalStack** (if not already running):
   ```powershell
   ./start.ps1
   ```

2. **Install dependencies and run all tests**:
   ```powershell
   ./test-suite.ps1
   ```

3. **Run specific test types**:
   ```powershell
   # Unit tests only
   ./test-suite.ps1 -TestType unit

   # API tests with coverage
   ./test-suite.ps1 -TestType api -Coverage

   # Watch mode for development
   ./test-suite.ps1 -TestType unit -Watch
   ```

## Test Structure

```
tests/
├── unit/                 # Unit tests with mocked AWS services
│   ├── auth/
│   ├── posts/
│   ├── media/
│   └── users/
├── integration/          # Integration tests with LocalStack
├── api/                  # API Gateway endpoint tests
├── e2e/                  # End-to-end user journey tests
└── utils/                # Shared test utilities
    ├── test-data.ts      # Mock data generators
    ├── aws-helpers.ts    # AWS service helpers
    └── api-helpers.ts    # HTTP request helpers
```

## Test Types

### Unit Tests

Test individual Lambda functions with mocked AWS services:

```typescript
// Example: Testing auth handler with mocked Cognito
import { mockClient } from 'aws-sdk-client-mock';
import { CognitoIdentityProviderClient } from '@aws-sdk/client-cognito-identity-provider';

const cognitoMock = mockClient(CognitoIdentityProviderClient);

describe('Auth Handler', () => {
  it('should create user successfully', async () => {
    cognitoMock.on(AdminCreateUserCommand).resolves({
      User: { Username: 'test-user-id' }
    });
    
    const result = await handler(event, context);
    expect(result.statusCode).toBe(201);
  });
});
```

### Integration Tests

Test AWS service interactions using LocalStack:

```typescript
// Example: Testing DynamoDB operations
import { DynamoDBHelper } from '../utils/aws-helpers';

describe('User Database Operations', () => {
  beforeAll(async () => {
    await DynamoDBHelper.createTable('Users', keySchema, attributeDefinitions);
  });

  it('should store user in database', async () => {
    const user = TestDataGenerator.createUser();
    await DynamoDBHelper.putItem('Users', user);
    
    // Verify user was stored
    const storedUser = await DynamoDBHelper.getItem('Users', { id: user.id });
    expect(storedUser).toEqual(user);
  });
});
```

### API Tests

Test complete HTTP flows through API Gateway:

```typescript
// Example: Testing auth endpoints
import { AuthApiClient } from '../utils/api-helpers';

describe('Auth API', () => {
  it('should authenticate user', async () => {
    const authClient = new AuthApiClient();
    const response = await authClient.signin('<EMAIL>', 'password');
    
    expect(response.statusCode).toBe(200);
    expect(response.body.tokens.access_token).toBeDefined();
  });
});
```

### End-to-End Tests

Test complete user journeys:

```typescript
// Example: Complete user registration and content creation
describe('User Journey', () => {
  it('should complete full user flow', async () => {
    // 1. Register user
    const signupResponse = await authClient.signup(email, password, username);
    expect(signupResponse.statusCode).toBe(201);
    
    // 2. Sign in
    const signinResponse = await authClient.signin(email, password);
    expect(signinResponse.statusCode).toBe(200);
    
    // 3. Create content
    postsClient.setAuthToken(signinResponse.body.tokens.access_token);
    const postResponse = await postsClient.createPost('My Post', 'Content');
    expect(postResponse.statusCode).toBe(201);
  });
});
```

## Test Utilities

### Test Data Generator

```typescript
import { TestDataGenerator } from './utils/test-data';

// Generate test users
const user = TestDataGenerator.createUser();
const post = TestDataGenerator.createPost(user.id);

// Generate API Gateway events
const event = TestDataGenerator.createAPIGatewayEvent({
  httpMethod: 'POST',
  path: '/auth/signup',
  body: JSON.stringify({ email: '<EMAIL>' })
});
```

### AWS Helpers

```typescript
import { DynamoDBHelper, S3Helper, CognitoHelper } from './utils/aws-helpers';

// DynamoDB operations
await DynamoDBHelper.createTable('Users', keySchema, attributeDefinitions);
await DynamoDBHelper.putItem('Users', userData);
await DynamoDBHelper.clearTable('Users');

// S3 operations
await S3Helper.createBucket('test-bucket');
await S3Helper.putObject('test-bucket', 'key', 'content');

// Cognito operations
const userPoolId = await CognitoHelper.createUserPool('test-pool');
await CognitoHelper.createUser(userPoolId, '<EMAIL>', 'password');
```

### API Helpers

```typescript
import { AuthApiClient, expectSuccessResponse } from './utils/api-helpers';

const authClient = new AuthApiClient();
const response = await authClient.signup('email', 'password', 'username');

expectSuccessResponse(response, 201);
expectCorsHeaders(response);
```

## Running Tests

### Development

```powershell
# Run all tests
./test-suite.ps1

# Run specific test types
./test-suite.ps1 -TestType unit
./test-suite.ps1 -TestType integration
./test-suite.ps1 -TestType api
./test-suite.ps1 -TestType e2e

# Development with watch mode
./test-suite.ps1 -TestType unit -Watch

# Generate coverage report
./test-suite.ps1 -Coverage
```

### CI/CD

```powershell
# Full CI pipeline
./test-ci.ps1

# Quick tests (unit + api only)
./test-ci.ps1 -Quick

# Skip E2E tests
./test-ci.ps1 -SkipE2E
```

### NPM Scripts

```bash
# Individual test commands
npm test                    # Run all tests
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:api           # API tests only
npm run test:e2e           # E2E tests only
npm run test:coverage      # All tests with coverage
npm run test:watch         # Watch mode
```

## Configuration

### Jest Configuration

The Jest configuration is in `jest.config.js` and includes:

- TypeScript support with ts-jest
- Multiple test environments (unit, integration, api, e2e)
- Coverage reporting
- Custom matchers for UUIDs, emails, dates
- LocalStack environment setup

### Environment Variables

Tests use these environment variables:

```bash
AWS_ENDPOINT_URL=http://localhost:45660
AWS_DEFAULT_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
COGNITO_USER_POOL_ID=us-east-1_TestPool
COGNITO_USER_POOL_CLIENT_ID=test-client-id
```

## Custom Matchers

The framework includes custom Jest matchers:

```typescript
expect(userId).toBeValidUUID();
expect(email).toBeValidEmail();
expect(timestamp).toBeValidISODate();
```

## Coverage Reports

Coverage reports are generated in multiple formats:

- **HTML**: `coverage/lcov-report/index.html`
- **LCOV**: `coverage/lcov.info`
- **JSON**: `coverage/coverage-final.json`
- **Text**: Console output

## Troubleshooting

### LocalStack Not Running

```
Error: LocalStack is not running or not accessible
```

**Solution**: Start LocalStack with `./start.ps1`

### Missing Dependencies

```
Error: Cannot find module 'jest'
```

**Solution**: Install dependencies with `npm install`

### Test Timeouts

```
Error: Timeout - Async callback was not invoked within the 30000ms timeout
```

**Solution**: Increase timeout in Jest configuration or specific tests

### AWS Service Errors

```
Error: UnknownEndpoint: Inaccessible host
```

**Solution**: Verify LocalStack is running and all required services are available

## Best Practices

1. **Isolation**: Each test should be independent and clean up after itself
2. **Mocking**: Use mocks for unit tests, real services for integration tests
3. **Data**: Use test data generators for consistent test data
4. **Assertions**: Use specific assertions and custom matchers
5. **Coverage**: Aim for >80% code coverage
6. **Performance**: Keep tests fast, use parallel execution where possible

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use the provided test utilities
3. Add appropriate cleanup in `afterEach`/`afterAll`
4. Update this documentation if adding new patterns
5. Ensure tests pass in CI environment
