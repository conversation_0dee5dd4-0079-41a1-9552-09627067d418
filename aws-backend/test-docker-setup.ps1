# GameFlex Docker Setup Test Script
# This script tests the new Docker-based AWS backend setup

param(
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
}

function Write-Failure {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
}

# Test Docker availability
function Test-DockerAvailability {
    Write-Header "Testing Docker availability..."
    
    try {
        docker --version | Out-Null
        Write-Success "Docker is available"
        
        docker compose version | Out-Null
        Write-Success "Docker Compose is available"
        
        return $true
    }
    catch {
        Write-Failure "Docker or Docker Compose is not available"
        return $false
    }
}

# Test Docker files exist
function Test-DockerFiles {
    Write-Header "Testing Docker configuration files..."
    
    $requiredFiles = @(
        "docker-compose.yml",
        "docker/lambda-builder.Dockerfile",
        "docker/deployer.Dockerfile",
        "docker/build-all-lambdas.sh",
        "docker/deploy-infrastructure.sh",
        "docker/init-gameflex.sh"
    )
    
    $allExist = $true
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-Success "Found: $file"
        }
        else {
            Write-Failure "Missing: $file"
            $allExist = $false
        }
    }
    
    return $allExist
}

# Test Lambda function source code
function Test-LambdaFunctions {
    Write-Header "Testing Lambda function source code..."
    
    $lambdaFunctions = @("auth", "posts", "users", "media")
    $allExist = $true
    
    foreach ($func in $lambdaFunctions) {
        $funcPath = "lambda-functions/$func"
        if (Test-Path $funcPath) {
            Write-Success "Found Lambda function: $func"
            
            # Check for package.json
            if (Test-Path "$funcPath/package.json") {
                Write-Success "  ✓ package.json exists"
            }
            else {
                Write-Failure "  ✗ package.json missing"
                $allExist = $false
            }
            
            # Check for TypeScript source
            if ((Test-Path "$funcPath/src") -or (Test-Path "$funcPath/*.ts")) {
                Write-Success "  ✓ TypeScript source found"
            }
            else {
                Write-Failure "  ✗ TypeScript source missing"
                $allExist = $false
            }
        }
        else {
            Write-Failure "Missing Lambda function: $func"
            $allExist = $false
        }
    }
    
    return $allExist
}

# Test CloudFormation templates
function Test-CloudFormationTemplates {
    Write-Header "Testing CloudFormation templates..."
    
    $requiredTemplates = @(
        "cloudformation/gameflex-infrastructure.yaml",
        "cloudformation/parameters/development.json"
    )
    
    $allExist = $true
    foreach ($template in $requiredTemplates) {
        if (Test-Path $template) {
            Write-Success "Found: $template"
        }
        else {
            Write-Failure "Missing: $template"
            $allExist = $false
        }
    }
    
    return $allExist
}

# Test build process
function Test-BuildProcess {
    Write-Header "Testing Lambda build process..."
    
    try {
        Write-Status "Running Lambda builder..."
        docker compose --profile build up lambda-builder
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Lambda build completed successfully"
            
            # Check if packages were created
            if (Test-Path "packages") {
                $packages = Get-ChildItem "packages/*.zip" -ErrorAction SilentlyContinue
                if ($packages.Count -gt 0) {
                    Write-Success "Found $($packages.Count) Lambda packages"
                    foreach ($package in $packages) {
                        Write-Status "  📦 $(Split-Path $package -Leaf)"
                    }
                    return $true
                }
                else {
                    Write-Failure "No Lambda packages found"
                    return $false
                }
            }
            else {
                Write-Failure "Packages directory not created"
                return $false
            }
        }
        else {
            Write-Failure "Lambda build failed"
            return $false
        }
    }
    catch {
        Write-Failure "Lambda build process failed: $_"
        return $false
    }
}

# Test LocalStack startup
function Test-LocalStackStartup {
    Write-Header "Testing LocalStack startup..."
    
    try {
        Write-Status "Starting LocalStack..."
        docker compose up -d localstack
        
        # Wait for LocalStack to be ready
        $timeout = 60
        $counter = 0
        
        do {
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:45660/_localstack/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Success "LocalStack is ready"
                    return $true
                }
            }
            catch {
                # Continue waiting
            }
            
            Start-Sleep -Seconds 2
            $counter += 2
            
            if ($counter -ge $timeout) {
                Write-Failure "LocalStack failed to start within $timeout seconds"
                return $false
            }
        } while ($true)
    }
    catch {
        Write-Failure "LocalStack startup failed: $_"
        return $false
    }
}

# Main test execution
function Main {
    Write-Header "GameFlex Docker Setup Test Suite"
    Write-Host ""
    
    $testResults = @()
    
    # Run tests
    $testResults += @{ Name = "Docker Availability"; Result = (Test-DockerAvailability) }
    $testResults += @{ Name = "Docker Files"; Result = (Test-DockerFiles) }
    $testResults += @{ Name = "Lambda Functions"; Result = (Test-LambdaFunctions) }
    $testResults += @{ Name = "CloudFormation Templates"; Result = (Test-CloudFormationTemplates) }
    $testResults += @{ Name = "Build Process"; Result = (Test-BuildProcess) }
    $testResults += @{ Name = "LocalStack Startup"; Result = (Test-LocalStackStartup) }
    
    # Summary
    Write-Host ""
    Write-Header "Test Results Summary"
    
    $passed = 0
    $failed = 0
    
    foreach ($test in $testResults) {
        if ($test.Result) {
            Write-Success "$($test.Name): PASSED"
            $passed++
        }
        else {
            Write-Failure "$($test.Name): FAILED"
            $failed++
        }
    }
    
    Write-Host ""
    Write-Status "Tests completed: $passed passed, $failed failed"
    
    if ($failed -eq 0) {
        Write-Success "All tests passed! The Docker setup is ready."
        return $true
    }
    else {
        Write-Failure "Some tests failed. Please fix the issues before proceeding."
        return $false
    }
}

# Cleanup function
function Cleanup {
    Write-Status "Cleaning up test resources..."
    try {
        docker compose down 2>$null
    }
    catch {
        # Ignore cleanup errors
    }
}

# Run tests
try {
    $result = Main
    if (-not $result) {
        exit 1
    }
}
catch {
    Write-Error "Test execution failed: $_"
    exit 1
}
finally {
    Cleanup
}
