/**
 * GameFlex Users Lambda Functions
 * Handles user profile operations using AWS DynamoDB
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { CognitoIdentityProviderClient, GetUserCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';

// AWS clients
const cognitoClient = new CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

// DynamoDB configuration
const dynamoClient = new DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// Table names
const USERS_TABLE = 'Users';
const USER_PROFILES_TABLE = 'UserProfiles';
const POSTS_TABLE = 'Posts';
const LIKES_TABLE = 'Likes';
const FOLLOWS_TABLE = 'Follows';

interface User {
    id: string;
    cognito_user_id: string;
    email: string;
    username: string;
    display_name?: string;
    bio?: string;
    avatar_url?: string;
    is_active: boolean;
    is_verified: boolean;
    created_at: string;
    updated_at: string;
    last_login?: string;
}

interface UserProfile {
    user_id: string;
    first_name?: string;
    last_name?: string;
    country?: string;
    timezone?: string;
    language?: string;
    created_at: string;
    updated_at: string;
}

interface UserStats {
    posts: number;
    followers: number;
    following: number;
    likes: number;
}

// CORS headers
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
    'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
    'Access-Control-Allow-Credentials': 'true'
};

function createCorsResponse(statusCode: number, body: any): APIGatewayProxyResult {
    return {
        statusCode,
        headers: corsHeaders,
        body: JSON.stringify(body)
    };
}

// Extract user ID from JWT token
function extractUserIdFromToken(authHeader: string): string | null {
    try {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }

        const token = authHeader.substring(7);
        const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        return payload.sub || payload['cognito:username'] || null;
    } catch (error) {
        console.error('Error extracting user ID from token:', error);
        return null;
    }
}

// Get user by Cognito ID
async function getUserByCognitoId(cognitoUserId: string): Promise<User | null> {
    try {
        const result = await docClient.send(new ScanCommand({
            TableName: USERS_TABLE,
            FilterExpression: 'cognito_user_id = :cognitoUserId',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId
            }
        }));

        return result.Items && result.Items.length > 0 ? result.Items[0] as User : null;
    } catch (error) {
        console.error('Error getting user by Cognito ID:', error);
        return null;
    }
}

// Get user by ID
async function getUserById(userId: string): Promise<User | null> {
    try {
        const result = await docClient.send(new GetCommand({
            TableName: USERS_TABLE,
            Key: { id: userId }
        }));

        return result.Item as User || null;
    } catch (error) {
        console.error('Error getting user by ID:', error);
        return null;
    }
}

// Get user profile
async function getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
        const result = await docClient.send(new GetCommand({
            TableName: USER_PROFILES_TABLE,
            Key: { user_id: userId }
        }));

        return result.Item as UserProfile || null;
    } catch (error) {
        console.error('Error getting user profile:', error);
        return null;
    }
}

// Get user statistics
async function getUserStats(userId: string): Promise<UserStats> {
    try {
        // Get posts count
        const postsResult = await docClient.send(new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            },
            Select: 'COUNT'
        }));

        // Get likes count (posts liked by this user)
        const likesResult = await docClient.send(new ScanCommand({
            TableName: LIKES_TABLE,
            FilterExpression: 'user_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));

        // Get followers count
        const followersResult = await docClient.send(new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'following_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));

        // Get following count
        const followingResult = await docClient.send(new ScanCommand({
            TableName: FOLLOWS_TABLE,
            FilterExpression: 'follower_id = :userId',
            ExpressionAttributeValues: {
                ':userId': userId
            },
            Select: 'COUNT'
        }));

        return {
            posts: postsResult.Count || 0,
            likes: likesResult.Count || 0,
            followers: followersResult.Count || 0,
            following: followingResult.Count || 0
        };
    } catch (error) {
        console.error('Error getting user stats:', error);
        return { posts: 0, likes: 0, followers: 0, following: 0 };
    }
}

// Get user posts
async function getUserPosts(userId: string, limit: number = 20, offset: number = 0): Promise<any[]> {
    try {
        const result = await docClient.send(new ScanCommand({
            TableName: POSTS_TABLE,
            FilterExpression: 'user_id = :userId AND is_active = :isActive',
            ExpressionAttributeValues: {
                ':userId': userId,
                ':isActive': true
            }
        }));

        // Sort by created_at descending and apply pagination
        const posts = (result.Items || [])
            .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
            .slice(offset, offset + limit);

        return posts;
    } catch (error) {
        console.error('Error getting user posts:', error);
        return [];
    }
}

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('Users Lambda - Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, { message: 'CORS preflight successful' });
    }

    const path = event.path;
    const method = event.httpMethod;
    const authHeader = event.headers.Authorization || event.headers.authorization;

    try {
        // Extract user ID from token for authenticated requests
        let currentUserId: string | null = null;
        if (authHeader) {
            currentUserId = extractUserIdFromToken(authHeader);
        }

        // Route handling
        if (method === 'GET' && path.match(/^\/users\/profile\/[^\/]+$/)) {
            // GET /users/profile/{userId}
            const userId = path.split('/').pop()!;
            
            const user = await getUserById(userId);
            if (!user) {
                return createCorsResponse(404, { error: 'User not found' });
            }

            const profile = await getUserProfile(userId);
            const stats = await getUserStats(userId);

            return createCorsResponse(200, {
                user,
                profile,
                stats
            });
        }

        if (method === 'GET' && path.match(/^\/users\/posts\/[^\/]+$/)) {
            // GET /users/posts/{userId}
            const userId = path.split('/').pop()!;
            const limit = parseInt(event.queryStringParameters?.limit || '20');
            const offset = parseInt(event.queryStringParameters?.offset || '0');

            const posts = await getUserPosts(userId, limit, offset);

            return createCorsResponse(200, { posts });
        }

        if (method === 'PUT' && path === '/users/profile') {
            // PUT /users/profile - Update current user's profile
            if (!currentUserId) {
                return createCorsResponse(401, { error: 'Authentication required' });
            }

            const user = await getUserByCognitoId(currentUserId);
            if (!user) {
                return createCorsResponse(404, { error: 'User not found' });
            }

            const body = JSON.parse(event.body || '{}');
            const now = new Date().toISOString();

            // Update user record
            const userUpdates: any = { updated_at: now };
            if (body.display_name !== undefined) userUpdates.display_name = body.display_name;
            if (body.username !== undefined) userUpdates.username = body.username;
            if (body.bio !== undefined) userUpdates.bio = body.bio;
            if (body.avatar_url !== undefined) userUpdates.avatar_url = body.avatar_url;

            if (Object.keys(userUpdates).length > 1) { // More than just updated_at
                await docClient.send(new UpdateCommand({
                    TableName: USERS_TABLE,
                    Key: { id: user.id },
                    UpdateExpression: `SET ${Object.keys(userUpdates).map(key => `#${key} = :${key}`).join(', ')}`,
                    ExpressionAttributeNames: Object.keys(userUpdates).reduce((acc, key) => {
                        acc[`#${key}`] = key;
                        return acc;
                    }, {} as any),
                    ExpressionAttributeValues: Object.keys(userUpdates).reduce((acc, key) => {
                        acc[`:${key}`] = userUpdates[key];
                        return acc;
                    }, {} as any)
                }));
            }

            // Update user profile
            const profileUpdates: any = { updated_at: now };
            if (body.first_name !== undefined) profileUpdates.first_name = body.first_name;
            if (body.last_name !== undefined) profileUpdates.last_name = body.last_name;
            if (body.country !== undefined) profileUpdates.country = body.country;
            if (body.timezone !== undefined) profileUpdates.timezone = body.timezone;
            if (body.language !== undefined) profileUpdates.language = body.language;

            if (Object.keys(profileUpdates).length > 1) { // More than just updated_at
                // Check if profile exists
                const existingProfile = await getUserProfile(user.id);
                
                if (existingProfile) {
                    // Update existing profile
                    await docClient.send(new UpdateCommand({
                        TableName: USER_PROFILES_TABLE,
                        Key: { user_id: user.id },
                        UpdateExpression: `SET ${Object.keys(profileUpdates).map(key => `#${key} = :${key}`).join(', ')}`,
                        ExpressionAttributeNames: Object.keys(profileUpdates).reduce((acc, key) => {
                            acc[`#${key}`] = key;
                            return acc;
                        }, {} as any),
                        ExpressionAttributeValues: Object.keys(profileUpdates).reduce((acc, key) => {
                            acc[`:${key}`] = profileUpdates[key];
                            return acc;
                        }, {} as any)
                    }));
                } else {
                    // Create new profile
                    await docClient.send(new PutCommand({
                        TableName: USER_PROFILES_TABLE,
                        Item: {
                            user_id: user.id,
                            created_at: now,
                            ...profileUpdates
                        }
                    }));
                }
            }

            return createCorsResponse(200, { message: 'Profile updated successfully' });
        }

        return createCorsResponse(404, { error: 'Endpoint not found' });

    } catch (error) {
        console.error('Users Lambda error:', error);
        return createCorsResponse(500, { 
            error: 'Internal server error',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};
