{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./lambda-functions/*"], "@auth/*": ["./lambda-functions/auth/src/*"], "@posts/*": ["./lambda-functions/posts/src/*"], "@media/*": ["./lambda-functions/media/src/*"], "@users/*": ["./lambda-functions/users/src/*"], "@tests/*": ["./tests/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "types": ["jest", "node"]}, "include": ["lambda-functions/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "lambda-functions/*/node_modules", "lambda-functions/*/dist", "coverage"]}