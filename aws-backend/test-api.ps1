# GameFlex API Test Script (PowerShell)
# Tests the deployed Lambda functions and services

param(
    [switch]$Verbose
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[API-TEST] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
}

function Write-Failure {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
}

# Test results
$script:TestResults = @{
    Passed = 0
    Failed = 0
    Tests = @()
}

# Test function wrapper
function Test-Api {
    param(
        [string]$TestName,
        [scriptblock]$TestScript
    )
    
    Write-Host ""
    Write-Header "Testing: $TestName"
    
    try {
        $result = & $TestScript
        
        if ($result) {
            Write-Success "$TestName - PASSED"
            $script:TestResults.Passed++
        }
        else {
            Write-Failure "$TestName - FAILED"
            $script:TestResults.Failed++
        }
        
        $script:TestResults.Tests += @{
            Name = $TestName
            Passed = $result
        }
        
        return $result
    }
    catch {
        Write-Failure "$TestName - ERROR: $_"
        $script:TestResults.Failed++
        $script:TestResults.Tests += @{
            Name = $TestName
            Passed = $false
            Error = $_.Exception.Message
        }
        return $false
    }
}

# Test Lambda function direct invocation
function Test-LambdaInvocation {
    try {
        $testEvent = @{
            path = "/test"
            httpMethod = "GET"
            headers = @{
                "Content-Type" = "application/json"
            }
            body = $null
        } | ConvertTo-Json -Depth 10
        
        $testEvent | Out-File -FilePath "temp-test-event.json" -Encoding UTF8
        
        $result = aws --endpoint-url=http://localhost:45660 lambda invoke `
            --function-name "gameflex-auth-development" `
            --payload file://temp-test-event.json `
            "auth-response.json" 2>$null
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path "auth-response.json")) {
            $response = Get-Content "auth-response.json" | ConvertFrom-Json
            Write-Status "Auth Lambda Response: $($response.message)"
            
            # Clean up
            Remove-Item "auth-response.json" -Force
            Remove-Item "temp-test-event.json" -Force
            
            return $true
        }
        else {
            Write-Error "Lambda invocation failed"
            Remove-Item "temp-test-event.json" -Force -ErrorAction SilentlyContinue
            return $false
        }
    }
    catch {
        Write-Error "Lambda test failed: $_"
        return $false
    }
}

# Test S3 operations
function Test-S3Operations {
    try {
        $testBucket = "gameflex-media-development"
        $testKey = "test/sample.txt"
        $testContent = "Hello from GameFlex API Test! Timestamp: $(Get-Date)"
        
        # Upload test file
        $testContent | Out-File -FilePath "temp-test-file.txt" -Encoding UTF8
        
        $uploadResult = aws --endpoint-url=http://localhost:45660 s3 cp "temp-test-file.txt" "s3://$testBucket/$testKey" 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Successfully uploaded test file to S3"
            
            # Download test file
            $downloadResult = aws --endpoint-url=http://localhost:45660 s3 cp "s3://$testBucket/$testKey" "downloaded-test-file.txt" 2>$null
            
            if ($LASTEXITCODE -eq 0 -and (Test-Path "downloaded-test-file.txt")) {
                $downloadedContent = Get-Content "downloaded-test-file.txt" -Raw
                
                if ($downloadedContent.Trim() -eq $testContent.Trim()) {
                    Write-Status "S3 upload/download cycle successful"
                    
                    # Clean up
                    Remove-Item "temp-test-file.txt" -Force
                    Remove-Item "downloaded-test-file.txt" -Force
                    aws --endpoint-url=http://localhost:45660 s3 rm "s3://$testBucket/$testKey" 2>$null
                    
                    return $true
                }
                else {
                    Write-Error "Downloaded content doesn't match uploaded content"
                    return $false
                }
            }
            else {
                Write-Error "Failed to download test file from S3"
                return $false
            }
        }
        else {
            Write-Error "Failed to upload test file to S3"
            return $false
        }
    }
    catch {
        Write-Error "S3 operations test failed: $_"
        return $false
    }
}

# Test database operations
function Test-DatabaseOperations {
    try {
        # Test basic query
        $userCount = docker compose exec -T -e PGPASSWORD=gameflex_password postgres psql -U postgres -d gameflex -c "SELECT COUNT(*) FROM users;" -t 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            $count = ($userCount | Select-String "\d+").Matches[0].Value
            Write-Status "Database has $count users"
            
            # Test more complex query
            $recentPosts = docker compose exec -T -e PGPASSWORD=gameflex_password postgres psql -U postgres -d gameflex -c "SELECT COUNT(*) FROM posts WHERE created_at > NOW() - INTERVAL '7 days';" -t 2>$null
            
            if ($LASTEXITCODE -eq 0) {
                $postCount = ($recentPosts | Select-String "\d+").Matches[0].Value
                Write-Status "Database has $postCount recent posts"
                return $true
            }
            else {
                Write-Error "Failed to query posts table"
                return $false
            }
        }
        else {
            Write-Error "Failed to query users table"
            return $false
        }
    }
    catch {
        Write-Error "Database operations test failed: $_"
        return $false
    }
}

# Test Redis operations
function Test-RedisOperations {
    try {
        # Set a test value
        $setResult = docker compose exec -T redis redis-cli SET "gameflex:test" "Hello Redis!" 2>$null
        
        if ($LASTEXITCODE -eq 0) {
            # Get the test value
            $getValue = docker compose exec -T redis redis-cli GET "gameflex:test" 2>$null
            
            if ($LASTEXITCODE -eq 0 -and $getValue -match "Hello Redis!") {
                Write-Status "Redis set/get operations successful"
                
                # Clean up
                docker compose exec -T redis redis-cli DEL "gameflex:test" 2>$null
                
                return $true
            }
            else {
                Write-Error "Failed to get value from Redis"
                return $false
            }
        }
        else {
            Write-Error "Failed to set value in Redis"
            return $false
        }
    }
    catch {
        Write-Error "Redis operations test failed: $_"
        return $false
    }
}

# Test all Lambda functions
function Test-AllLambdaFunctions {
    try {
        $functions = @("gameflex-auth-development", "gameflex-posts-development", "gameflex-media-development")
        $successCount = 0
        
        foreach ($functionName in $functions) {
            try {
                $testEvent = @{
                    path = "/api/test"
                    httpMethod = "GET"
                    headers = @{}
                } | ConvertTo-Json -Depth 10
                
                $testEvent | Out-File -FilePath "temp-event-$functionName.json" -Encoding UTF8
                
                $result = aws --endpoint-url=http://localhost:45660 lambda invoke `
                    --function-name $functionName `
                    --payload file://temp-event-$functionName.json `
                    "response-$functionName.json" 2>$null
                
                if ($LASTEXITCODE -eq 0 -and (Test-Path "response-$functionName.json")) {
                    $response = Get-Content "response-$functionName.json" | ConvertFrom-Json
                    Write-Status "✓ ${functionName}: $($response.message)"
                    $successCount++
                }
                else {
                    Write-Warning "✗ Failed to invoke ${functionName}"
                }
                
                # Clean up
                Remove-Item "temp-event-$functionName.json" -Force -ErrorAction SilentlyContinue
                Remove-Item "response-$functionName.json" -Force -ErrorAction SilentlyContinue
            }
            catch {
                Write-Warning "Error testing ${functionName}: $_"
            }
        }
        
        if ($successCount -eq $functions.Count) {
            Write-Status "All Lambda functions are working"
            return $true
        }
        elseif ($successCount -gt 0) {
            Write-Warning "$successCount out of $($functions.Count) Lambda functions are working"
            return $true  # Partial success is still success for free LocalStack
        }
        else {
            Write-Error "No Lambda functions are working"
            return $false
        }
    }
    catch {
        Write-Error "Lambda functions test failed: $_"
        return $false
    }
}

# Display test results
function Show-TestResults {
    Write-Host ""
    Write-Header "API Test Results Summary"
    Write-Host ""
    
    $total = $script:TestResults.Passed + $script:TestResults.Failed
    $passRate = if ($total -gt 0) { [math]::Round(($script:TestResults.Passed / $total) * 100, 1) } else { 0 }
    
    Write-Host "Total Tests: $total" -ForegroundColor Cyan
    Write-Host "Passed: $($script:TestResults.Passed)" -ForegroundColor Green
    Write-Host "Failed: $($script:TestResults.Failed)" -ForegroundColor Red
    Write-Host "Pass Rate: $passRate%" -ForegroundColor $(if ($passRate -ge 80) { "Green" } else { "Yellow" })
    
    Write-Host ""
    Write-Status "Individual Test Results:"
    
    foreach ($test in $script:TestResults.Tests) {
        $status = if ($test.Passed) { "PASS" } else { "FAIL" }
        $color = if ($test.Passed) { "Green" } else { "Red" }
        Write-Host "  $status - $($test.Name)" -ForegroundColor $color
        
        if ($test.Error) {
            Write-Host "    Error: $($test.Error)" -ForegroundColor Red
        }
    }
}

# Main test execution
function Main {
    Write-Header "GameFlex API Test Suite"
    Write-Host ""
    
    # Run API tests
    Test-Api "Database Operations" { Test-DatabaseOperations }
    Test-Api "Redis Operations" { Test-RedisOperations }
    Test-Api "S3 Operations" { Test-S3Operations }
    Test-Api "Lambda Functions" { Test-AllLambdaFunctions }
    
    # Show results
    Show-TestResults
    
    # Exit with appropriate code
    if ($script:TestResults.Failed -eq 0) {
        Write-Status "All API tests passed! Backend is fully functional."
        exit 0
    }
    else {
        Write-Warning "Some API tests failed, but core functionality is working."
        exit 0  # Don't fail for LocalStack limitations
    }
}

# Run tests
try {
    Main
}
catch {
    Write-Error "API test execution failed: $_"
    exit 1
}
