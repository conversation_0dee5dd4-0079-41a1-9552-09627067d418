-- Comments Functions and Enhanced RLS Policies
-- This file adds missing database functions and policies for comment management

-- Drop existing functions to avoid conflicts
DROP FUNCTION IF EXISTS public.increment_post_comments(UUID);
DROP FUNCTION IF EXISTS public.decrement_post_comments(UUID);
DROP FUNCTION IF EXISTS public.update_post_comment_count();

-- Function to increment comment count for a post
CREATE OR REPLACE FUNCTION public.increment_post_comments(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.posts
    SET comment_count = comment_count + 1
    WHERE id = post_id;
END;
$$ language 'plpgsql';

-- Function to decrement comment count for a post
CREATE OR REPLACE FUNCTION public.decrement_post_comments(post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.posts
    SET comment_count = GREATEST(comment_count - 1, 0)
    WHERE id = post_id;
END;
$$ language 'plpgsql';

-- Function to recalculate comment count for a post (for data consistency)
DROP FUNCTION IF EXISTS public.recalculate_post_comment_count(UUID);
CREATE OR REPLACE FUNCTION public.recalculate_post_comment_count(target_post_id UUID)
RETURNS void AS $$
DECLARE
    comment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO comment_count
    FROM public.comments
    WHERE post_id = target_post_id AND is_active = true;

    UPDATE public.posts
    SET comment_count = recalculate_post_comment_count.comment_count
    WHERE id = target_post_id;
END;
$$ language 'plpgsql';

-- Function to increment comment likes
CREATE OR REPLACE FUNCTION public.increment_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.comments
    SET like_count = like_count + 1
    WHERE id = comment_id;
END;
$$ language 'plpgsql';

-- Function to decrement comment likes
CREATE OR REPLACE FUNCTION public.decrement_comment_likes(comment_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.comments
    SET like_count = GREATEST(like_count - 1, 0)
    WHERE id = comment_id;
END;
$$ language 'plpgsql';

-- Trigger function to automatically update post comment count when comments are added/removed
CREATE OR REPLACE FUNCTION public.update_post_comment_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- New comment added
        PERFORM public.increment_post_comments(NEW.post_id);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Comment updated (check if is_active changed)
        IF OLD.is_active = true AND NEW.is_active = false THEN
            -- Comment was deactivated (soft deleted)
            PERFORM public.decrement_post_comments(NEW.post_id);
        ELSIF OLD.is_active = false AND NEW.is_active = true THEN
            -- Comment was reactivated
            PERFORM public.increment_post_comments(NEW.post_id);
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Comment hard deleted
        IF OLD.is_active = true THEN
            PERFORM public.decrement_post_comments(OLD.post_id);
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ language 'plpgsql';

-- Create trigger for automatic comment count updates
DROP TRIGGER IF EXISTS update_post_comment_count_trigger ON public.comments;
CREATE TRIGGER update_post_comment_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION public.update_post_comment_count();

-- Enhanced RLS Policies for Comments
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Comments are viewable by everyone" ON public.comments;
DROP POLICY IF EXISTS "Users can insert their own comments" ON public.comments;
DROP POLICY IF EXISTS "Users can update their own comments" ON public.comments;
DROP POLICY IF EXISTS "Users can delete their own comments" ON public.comments;

-- Create comprehensive RLS policies for comments
CREATE POLICY "Comments are viewable by everyone" ON public.comments
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can insert their own comments" ON public.comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON public.comments
    FOR DELETE USING (auth.uid() = user_id);

-- Policy to allow users to soft delete their own comments by setting is_active = false
CREATE POLICY "Users can soft delete their own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id AND is_active = true)
    WITH CHECK (auth.uid() = user_id);

-- Enhanced RLS Policies for Posts (to allow comment count updates)
-- Allow system to update comment counts
CREATE POLICY "System can update post comment counts" ON public.posts
    FOR UPDATE USING (true)
    WITH CHECK (true);

-- Function to get comments with user information (for better performance)
DROP FUNCTION IF EXISTS public.get_comments_with_users(UUID, INTEGER, INTEGER);
CREATE OR REPLACE FUNCTION public.get_comments_with_users(target_post_id UUID, limit_count INTEGER DEFAULT 50, offset_count INTEGER DEFAULT 0)
RETURNS TABLE (
    id UUID,
    post_id UUID,
    user_id UUID,
    content TEXT,
    like_count INTEGER,
    is_active BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    username TEXT,
    display_name TEXT,
    avatar_url TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        c.id,
        c.post_id,
        c.user_id,
        c.content,
        c.like_count,
        c.is_active,
        c.created_at,
        c.updated_at,
        u.username,
        u.display_name,
        up.avatar_url
    FROM public.comments c
    INNER JOIN public.users u ON c.user_id = u.id
    LEFT JOIN public.user_profiles up ON c.user_id = up.user_id
    WHERE c.post_id = target_post_id
        AND c.is_active = true
    ORDER BY c.created_at ASC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.increment_post_comments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.decrement_post_comments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.recalculate_post_comment_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.increment_comment_likes(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.decrement_comment_likes(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_comments_with_users(UUID, INTEGER, INTEGER) TO authenticated;

-- Ensure the trigger function has proper permissions
GRANT EXECUTE ON FUNCTION public.update_post_comment_count() TO authenticated;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_comments_post_id_active ON public.comments(post_id, is_active);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON public.comments(created_at);

-- Recalculate comment counts for existing posts to ensure consistency
DO $$
DECLARE
    post_record RECORD;
BEGIN
    FOR post_record IN SELECT id FROM public.posts LOOP
        PERFORM public.recalculate_post_comment_count(post_record.id);
    END LOOP;
END $$;

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'Comments functions and enhanced RLS policies have been successfully applied';
END $$;
