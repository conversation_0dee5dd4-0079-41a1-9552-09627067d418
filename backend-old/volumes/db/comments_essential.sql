-- Essential Comments Functions and RLS Policies
-- This file adds the minimum required functions and policies for comment management

-- Drop and recreate the recalculate function with correct parameter naming
DROP FUNCTION IF EXISTS public.recalculate_post_comment_count(UUID);

CREATE OR REPLACE FUNCTION public.recalculate_post_comment_count(target_post_id UUID)
RET<PERSON>NS void AS $$
DECLARE
    actual_comment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO actual_comment_count
    FROM public.comments
    WHERE post_id = target_post_id AND is_active = true;
    
    UPDATE public.posts
    SET comment_count = actual_comment_count
    WHERE id = target_post_id;
END;
$$ language 'plpgsql';

-- Function to increment comment count for a post
CREATE OR REPLACE FUNCTION public.increment_post_comments(target_post_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.posts
    SET comment_count = comment_count + 1
    WHERE id = target_post_id;
END;
$$ language 'plpgsql';

-- Function to decrement comment count for a post
CREATE OR REPLACE FUNCTION public.decrement_post_comments(target_post_id UUID)
R<PERSON><PERSON><PERSON> void AS $$
BEGIN
    UPDATE public.posts
    SET comment_count = GREATEST(comment_count - 1, 0)
    WHERE id = target_post_id;
END;
$$ language 'plpgsql';

-- Add missing DELETE policy for comments
DROP POLICY IF EXISTS "Users can delete their own comments" ON public.comments;
CREATE POLICY "Users can delete their own comments" ON public.comments
    FOR DELETE USING (auth.uid() = user_id);

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION public.increment_post_comments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.decrement_post_comments(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.recalculate_post_comment_count(UUID) TO authenticated;

-- Recalculate comment counts for existing posts to ensure consistency
DO $$
DECLARE
    post_record RECORD;
BEGIN
    FOR post_record IN SELECT id FROM public.posts LOOP
        PERFORM public.recalculate_post_comment_count(post_record.id);
    END LOOP;
    RAISE NOTICE 'Comment counts recalculated for all posts';
END $$;

-- Log completion
SELECT 'Essential comments functions and RLS policies have been successfully applied' AS status;
