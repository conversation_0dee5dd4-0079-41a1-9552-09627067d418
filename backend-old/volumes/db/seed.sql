-- GameFlex Development Seed Data
-- This script inserts initial development data into the public schema
-- Note: Auth users are created separately by scripts/init-auth-users.sh after services are ready

-- <PERSON><PERSON> function to automatically create user profiles when auth users are created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Only create profile if user doesn't already exist in public.users (by ID or email)
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE id = NEW.id OR email = NEW.email) THEN
    INSERT INTO public.users (
      id,
      email,
      username,
      display_name,
      is_verified,
      is_active,
      created_at,
      updated_at
    ) VALUES (
      NEW.id,
      NEW.email,
      COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
      COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
      NEW.email_confirmed_at IS NOT NULL,
      true,
      NEW.created_at,
      NEW.updated_at
    ) ON CONFLICT (email) DO NOTHING;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profiles
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert development users into public.users table
INSERT INTO public.users (id, email, username, display_name, bio, is_verified, is_active) VALUES
(
    '********-0000-0000-0000-********0001',
    '<EMAIL>',
    'devuser',
    'Development User',
    'This is a development user account for testing purposes.',
    true,
    true
),
(
    '********-0000-0000-0000-********0002',
    '<EMAIL>',
    'admin',
    'Admin User',
    'Administrator account for GameFlex development.',
    true,
    true
),
(
    '********-0000-0000-0000-********0003',
    '<EMAIL>',
    'johndoe',
    'John Doe',
    'Gaming enthusiast and content creator.',
    true,
    true
),
(
    '********-0000-0000-0000-********0004',
    '<EMAIL>',
    'janesmith',
    'Jane Smith',
    'Professional gamer and streamer.',
    true,
    true
),
(
    '********-0000-0000-0000-********0005',
    '<EMAIL>',
    'mikewilson',
    'Mike Wilson',
    'Casual gamer who loves sharing gaming moments.',
    true,
    true
)
ON CONFLICT (email) DO NOTHING;

-- Insert user profiles (only if user_profiles table exists and has the expected structure)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
        INSERT INTO public.user_profiles (user_id, first_name, last_name, country, language, preferences) VALUES
        (
            '********-0000-0000-0000-********0001',
            'Dev',
            'User',
            'United States',
            'en',
            '{"theme": "dark", "notifications": {"email": true, "push": true}}'
        ),
        (
            '********-0000-0000-0000-********0002',
            'Admin',
            'User',
            'United States',
            'en',
            '{"theme": "dark", "notifications": {"email": true, "push": true}}'
        ),
        (
            '********-0000-0000-0000-********0003',
            'John',
            'Doe',
            'United States',
            'en',
            '{"theme": "light", "notifications": {"email": true, "push": false}}'
        ),
        (
            '********-0000-0000-0000-********0004',
            'Jane',
            'Smith',
            'Canada',
            'en',
            '{"theme": "dark", "notifications": {"email": false, "push": true}}'
        ),
        (
            '********-0000-0000-0000-********0005',
            'Mike',
            'Wilson',
            'United Kingdom',
            'en',
            '{"theme": "auto", "notifications": {"email": true, "push": true}}'
        )
        ON CONFLICT (user_id) DO NOTHING;
        RAISE NOTICE 'User profiles inserted successfully';
    ELSE
        RAISE NOTICE 'user_profiles table does not exist, skipping profile insertion';
    END IF;
END
$$;

-- Insert development channels
INSERT INTO public.channels (id, name, description, owner_id, is_public, member_count) VALUES
(
    '10000000-0000-0000-0000-********0001',
    'General Gaming',
    'A place to discuss all things gaming',
    '********-0000-0000-0000-********0001',
    true,
    5
),
(
    '10000000-0000-0000-0000-********0002',
    'Mobile Gaming',
    'Share your favorite mobile gaming moments',
    '********-0000-0000-0000-********0003',
    true,
    3
),
(
    '10000000-0000-0000-0000-********0003',
    'Competitive Gaming',
    'For serious gamers and esports enthusiasts',
    '********-0000-0000-0000-********0004',
    true,
    2
),
(
    '10000000-0000-0000-0000-********0004',
    'Indie Games',
    'Discover and share amazing indie games',
    '********-0000-0000-0000-********0005',
    true,
    4
),
(
    '10000000-0000-0000-0000-********0005',
    'GameFlex Development',
    'Private channel for GameFlex development team',
    '********-0000-0000-0000-********0002',
    false,
    2
),
(
    '10000000-0000-0000-0000-********0006',
    'Minecraft',
    'Build, explore, and survive in the world of blocks',
    '********-0000-0000-0000-********0001',
    true,
    15
),
(
    '10000000-0000-0000-0000-********0007',
    'Fortnite',
    'Battle royale action and creative building',
    '********-0000-0000-0000-********0003',
    true,
    12
),
(
    '10000000-0000-0000-0000-********0008',
    'Valorant',
    'Tactical FPS with unique agent abilities',
    '********-0000-0000-0000-********0004',
    true,
    8
),
(
    '10000000-0000-0000-0000-********0009',
    'League of Legends',
    'The world''s most popular MOBA game',
    '********-0000-0000-0000-********0005',
    true,
    20
),
(
    '10000000-0000-0000-0000-********0010',
    'Apex Legends',
    'Fast-paced battle royale with unique legends',
    '********-0000-0000-0000-********0002',
    true,
    9
)
ON CONFLICT (id) DO NOTHING;

-- Insert channel memberships
INSERT INTO public.channel_members (channel_id, user_id, role) VALUES
-- General Gaming channel
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0001', 'owner'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0002', 'admin'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0004', 'member'),
('10000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'member'),

-- Mobile Gaming channel
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0003', 'owner'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'member'),

-- Competitive Gaming channel
('10000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0004', 'owner'),
('10000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0003', 'member'),

-- Indie Games channel
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0005', 'owner'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0004', 'member'),

-- GameFlex Development channel (private)
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0002', 'owner'),
('10000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'admin'),

-- Minecraft channel
('10000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0001', 'owner'),
('10000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0002', 'member'),
('10000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0004', 'member'),
('10000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0005', 'member'),

-- Fortnite channel
('10000000-0000-0000-0000-********0007', '********-0000-0000-0000-********0003', 'owner'),
('10000000-0000-0000-0000-********0007', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0007', '********-0000-0000-0000-********0004', 'member'),
('10000000-0000-0000-0000-********0007', '********-0000-0000-0000-********0005', 'member'),

-- Valorant channel
('10000000-0000-0000-0000-********0008', '********-0000-0000-0000-********0004', 'owner'),
('10000000-0000-0000-0000-********0008', '********-0000-0000-0000-********0002', 'member'),
('10000000-0000-0000-0000-********0008', '********-0000-0000-0000-********0003', 'member'),

-- League of Legends channel
('10000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0005', 'owner'),
('10000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0002', 'member'),
('10000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0004', 'member'),

-- Apex Legends channel
('10000000-0000-0000-0000-********0010', '********-0000-0000-0000-********0002', 'owner'),
('10000000-0000-0000-0000-********0010', '********-0000-0000-0000-********0001', 'member'),
('10000000-0000-0000-0000-********0010', '********-0000-0000-0000-********0003', 'member'),
('10000000-0000-0000-0000-********0010', '********-0000-0000-0000-********0005', 'member')
ON CONFLICT (channel_id, user_id) DO NOTHING;

-- Insert sample media records
INSERT INTO public.media (id, type, location, name, extension, channel_id, owner_id, bucket_location, bucket_name, bucket_permission) VALUES
(
    '30000000-0000-0000-0000-********0001',
    'image',
    'user',
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'jpg',
    '10000000-0000-0000-0000-********0001',
    '********-0000-0000-0000-********0003',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0002',
    'image',
    'user',
    'b2c3d4e5-f6g7-8901-bcde-f23456789012',
    'webp',
    '10000000-0000-0000-0000-********0003',
    '********-0000-0000-0000-********0004',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0003',
    'image',
    'user',
    'c3d4e5f6-g7h8-9012-cdef-345678901234',
    'webp',
    '10000000-0000-0000-0000-********0004',
    '********-0000-0000-0000-********0005',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0004',
    'image',
    'user',
    'd4e5f6g7-h8i9-0123-def0-456789012345',
    'png',
    '10000000-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0001',
    'storage/v1/object',
    'media',
    'public'
),
-- Media for channel posts
(
    '30000000-0000-0000-0000-********0005',
    'video',
    'user',
    'e5f6g7h8-i9j0-1234-efgh-567890123456',
    'mp4',
    '10000000-0000-0000-0000-********0001',
    '********-0000-0000-0000-********0002',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0006',
    'image',
    'minecraft',
    'castle_build',
    'jpg',
    '10000000-0000-0000-0000-********0006',
    '********-0000-0000-0000-********0001',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0007',
    'image',
    'minecraft',
    'diamonds',
    'png',
    '10000000-0000-0000-0000-********0006',
    '********-0000-0000-0000-********0003',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0008',
    'image',
    'fortnite',
    'victory_royale',
    'jpg',
    '10000000-0000-0000-0000-********0007',
    '********-0000-0000-0000-********0003',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0009',
    'video',
    'fortnite',
    'new_weapon',
    'mp4',
    '10000000-0000-0000-0000-********0007',
    '********-0000-0000-0000-********0004',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0010',
    'video',
    'valorant',
    'jett_ace',
    'mp4',
    '10000000-0000-0000-0000-********0008',
    '********-0000-0000-0000-********0004',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0011',
    'image',
    'valorant',
    'new_agent',
    'jpg',
    '10000000-0000-0000-0000-********0008',
    '********-0000-0000-0000-********0002',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0012',
    'video',
    'lol',
    'yasuo_pentakill',
    'mp4',
    '10000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0005',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0013',
    'image',
    'lol',
    'diamond_rank',
    'jpg',
    '10000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0001',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0014',
    'image',
    'lol',
    'new_champion',
    'jpg',
    '10000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0003',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0015',
    'video',
    'apex',
    'wraith_squad_wipe',
    'mp4',
    '10000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0002',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0016',
    'image',
    'apex',
    'storm_point',
    'jpg',
    '10000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0001',
    'storage/v1/object',
    'media',
    'public'
),
(
    '30000000-0000-0000-0000-********0017',
    'image',
    'apex',
    'predator_rank',
    'jpg',
    '10000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0005',
    'storage/v1/object',
    'media',
    'public'
);

-- Insert sample posts
-- First 4 posts are regular user posts (no channel_id) for home feed
-- Using static timestamps in descending order (most recent first)
INSERT INTO public.posts (id, user_id, content, media_id, like_count, comment_count, created_at, updated_at) VALUES
(
    '20000000-0000-0000-0000-********0001',
    '********-0000-0000-0000-********0003',
    'Just finished an amazing gaming session! What games are you all playing this weekend?',
    '30000000-0000-0000-0000-********0001',
    5,
    2,
    '2024-12-28 14:30:00+00',
    '2024-12-28 14:30:00+00'
),
(
    '20000000-0000-0000-0000-********0002',
    '********-0000-0000-0000-********0004',
    'New tournament starting next week! Who''s ready to compete?',
    '30000000-0000-0000-0000-********0002',
    8,
    3,
    '2024-12-27 16:45:00+00',
    '2024-12-27 16:45:00+00'
),
(
    '20000000-0000-0000-0000-********0003',
    '********-0000-0000-0000-********0005',
    'Found this incredible indie game that you all need to try!',
    '30000000-0000-0000-0000-********0003',
    12,
    4,
    '2024-12-26 10:15:00+00',
    '2024-12-26 10:15:00+00'
),
(
    '20000000-0000-0000-0000-********0004',
    '********-0000-0000-0000-********0001',
    'Mobile gaming has come so far! Check out this gameplay.',
    '30000000-0000-0000-0000-********0004',
    15,
    6,
    '2024-12-25 12:00:00+00',
    '2024-12-25 12:00:00+00'
);

-- Channel posts start here
-- Using static timestamps for channel posts (older than main feed posts)
INSERT INTO public.posts (id, user_id, channel_id, content, media_id, like_count, comment_count, created_at, updated_at) VALUES
(
    '20000000-0000-0000-0000-********0005',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Working on some exciting new features for GameFlex!',
    null,
    3,
    1,
    '2024-12-24 09:00:00+00',
    '2024-12-24 09:00:00+00'
),
(
    '20000000-0000-0000-0000-********0006',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0001',
    'Epic boss fight last night! Took us 3 hours but we finally got him down! 🎮⚔️',
    null,
    23,
    8,
    '2024-12-23 20:30:00+00',
    '2024-12-23 20:30:00+00'
),
(
    '20000000-0000-0000-0000-********0007',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0002',
    'Streaming live now! Come watch me attempt this impossible speedrun 🏃‍♂️💨',
    null,
    45,
    12,
    '2024-12-23 18:15:00+00',
    '2024-12-23 18:15:00+00'
),
(
    '20000000-0000-0000-0000-********0008',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0003',
    'Just discovered this hidden gem of a game. The art style is absolutely stunning! 🎨',
    null,
    18,
    5,
    '2024-12-22 15:45:00+00',
    '2024-12-22 15:45:00+00'
),
(
    '20000000-0000-0000-0000-********0009',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0004',
    'GameFlex community is growing so fast! Thank you all for being part of this journey 🚀',
    null,
    67,
    15,
    '2024-12-22 11:20:00+00',
    '2024-12-22 11:20:00+00'
),
(
    '20000000-0000-0000-0000-********0010',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'New patch notes are out! Lots of exciting changes coming to your favorite games 📝',
    null,
    34,
    9,
    '2024-12-21 14:30:00+00',
    '2024-12-21 14:30:00+00'
),
(
    '20000000-0000-0000-0000-********0011',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0001',
    'Retro gaming night! Playing some classics from the 90s. What''s your favorite retro game? 🕹️',
    null,
    29,
    11,
    '2024-12-21 19:45:00+00',
    '2024-12-21 19:45:00+00'
),
(
    '20000000-0000-0000-0000-********0012',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0002',
    'VR gaming is mind-blowing! Just tried the new space exploration game 🚀🌌',
    null,
    41,
    7,
    '2024-12-20 16:20:00+00',
    '2024-12-20 16:20:00+00'
),
(
    '20000000-0000-0000-0000-********0013',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0003',
    'Cozy gaming session with some hot chocolate ☕ Perfect way to spend a rainy evening',
    null,
    22,
    6,
    '2024-12-20 21:10:00+00',
    '2024-12-20 21:10:00+00'
),
(
    '20000000-0000-0000-0000-********0014',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0004',
    'Beta testing some amazing new features! Can''t wait for you all to try them 🎯',
    null,
    38,
    13,
    '2024-12-19 13:25:00+00',
    '2024-12-19 13:25:00+00'
),
(
    '20000000-0000-0000-0000-********0015',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0005',
    'Game development tip: Always playtest with real users! Their feedback is invaluable 💡',
    null,
    25,
    4,
    '2024-12-19 10:15:00+00',
    '2024-12-19 10:15:00+00'
),
(
    '20000000-0000-0000-0000-********0016',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0001',
    'Check out this amazing AI-powered game I''ve been working on! The future of gaming is here 🤖🎮',
    '30000000-0000-0000-0000-********0005',
    42,
    8,
    '2024-12-18 17:40:00+00',
    '2024-12-18 17:40:00+00'
),
-- Minecraft posts
(
    '20000000-0000-0000-0000-********0017',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0006',
    'Just finished building this massive castle! Took me 3 weeks but totally worth it 🏰',
    '30000000-0000-0000-0000-********0006',
    56,
    12,
    '2024-12-18 14:20:00+00',
    '2024-12-18 14:20:00+00'
),
(
    '20000000-0000-0000-0000-********0018',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0006',
    'Found diamonds at Y level 12! The new caves are amazing ⛏️💎',
    '30000000-0000-0000-0000-********0007',
    34,
    7,
    '2024-12-17 11:30:00+00',
    '2024-12-17 11:30:00+00'
),
-- Fortnite posts
(
    '20000000-0000-0000-0000-********0019',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0007',
    'Victory Royale with 15 eliminations! New season is fire 🔥👑',
    '30000000-0000-0000-0000-********0008',
    89,
    23,
    '2024-12-17 20:45:00+00',
    '2024-12-17 20:45:00+00'
),
(
    '20000000-0000-0000-0000-********0020',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0007',
    'This new weapon is absolutely broken! Epic needs to nerf it ASAP 😅',
    '30000000-0000-0000-0000-********0009',
    67,
    18,
    '2024-12-16 15:10:00+00',
    '2024-12-16 15:10:00+00'
),
-- Valorant posts
(
    '20000000-0000-0000-0000-********0021',
    '********-0000-0000-0000-********0004',
    '10000000-0000-0000-0000-********0008',
    'Ace clutch with Jett! The enemy team didn''t see it coming 🎯',
    '30000000-0000-0000-0000-********0010',
    124,
    31,
    '2024-12-16 22:30:00+00',
    '2024-12-16 22:30:00+00'
),
(
    '20000000-0000-0000-0000-********0022',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0008',
    'New agent looks insane! Can''t wait to try those abilities 🔥',
    '30000000-0000-0000-0000-********0011',
    78,
    15,
    '2024-12-15 16:20:00+00',
    '2024-12-15 16:20:00+00'
),
-- League of Legends posts
(
    '20000000-0000-0000-0000-********0023',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0009',
    'Pentakill with Yasuo! The enemy team got outplayed hard ⚔️',
    '30000000-0000-0000-0000-********0012',
    156,
    42,
    '2024-12-15 19:45:00+00',
    '2024-12-15 19:45:00+00'
),
(
    '20000000-0000-0000-0000-********0024',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0009',
    'Finally reached Diamond! The grind was real but so worth it 💎',
    '30000000-0000-0000-0000-********0013',
    98,
    27,
    '2024-12-14 21:15:00+00',
    '2024-12-14 21:15:00+00'
),
(
    '20000000-0000-0000-0000-********0025',
    '********-0000-0000-0000-********0003',
    '10000000-0000-0000-0000-********0009',
    'New champion spotlight is out! This kit looks absolutely broken 🤯',
    '30000000-0000-0000-0000-********0014',
    87,
    19,
    '2024-12-14 14:30:00+00',
    '2024-12-14 14:30:00+00'
),
-- Apex Legends posts
(
    '20000000-0000-0000-0000-********0026',
    '********-0000-0000-0000-********0002',
    '10000000-0000-0000-0000-********0010',
    'Squad wipe with Wraith! Portal plays are everything in ranked 🌀',
    '30000000-0000-0000-0000-********0015',
    112,
    28,
    '2024-12-13 18:25:00+00',
    '2024-12-13 18:25:00+00'
),
(
    '20000000-0000-0000-0000-********0027',
    '********-0000-0000-0000-********0001',
    '10000000-0000-0000-0000-********0010',
    'New map rotation is live! Storm Point is back and I''m so hyped 🏔️',
    '30000000-0000-0000-0000-********0016',
    73,
    16,
    '2024-12-13 12:40:00+00',
    '2024-12-13 12:40:00+00'
),
(
    '20000000-0000-0000-0000-********0028',
    '********-0000-0000-0000-********0005',
    '10000000-0000-0000-0000-********0010',
    'Predator rank achieved! Time to celebrate with the squad 🎉',
    '30000000-0000-0000-0000-********0017',
    134,
    35,
    '2024-12-12 20:15:00+00',
    '2024-12-12 20:15:00+00'
)
ON CONFLICT (id) DO NOTHING;

-- Insert sample comments
INSERT INTO public.comments (post_id, user_id, content, like_count) VALUES
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0004', 'I''m playing the new RPG that just came out!', 2),
('20000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0005', 'Same here! It''s incredible.', 1),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0003', 'Count me in! When do registrations open?', 3),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0005', 'I''ve been practicing for weeks!', 2),
('20000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0001', 'This is going to be epic!', 1),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0001', 'What''s the name of the game?', 4),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0003', 'I love discovering new indie games!', 2),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0004', 'Thanks for the recommendation!', 1),
('20000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0002', 'Adding this to my wishlist!', 1),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0003', 'Mobile gaming is the future!', 3),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0004', 'The graphics look amazing!', 2),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0005', 'Can''t wait to try this game!', 1),
('20000000-0000-0000-0000-********0004', '********-0000-0000-0000-********0002', 'Great content as always!', 2),
('20000000-0000-0000-0000-********0005', '********-0000-0000-0000-********0001', 'Excited to see what''s coming!', 1),
('20000000-0000-0000-0000-********0016', '********-0000-0000-0000-********0001', 'This looks incredible! AI in gaming is so exciting 🤯', 5),
('20000000-0000-0000-0000-********0016', '********-0000-0000-0000-********0003', 'The future is now! Can''t wait to play this', 3),
('20000000-0000-0000-0000-********0016', '********-0000-0000-0000-********0004', 'Amazing work! How long did this take to develop?', 2),
('20000000-0000-0000-0000-********0016', '********-0000-0000-0000-********0005', 'AI gaming is revolutionary! Great job 👏', 4)
ON CONFLICT DO NOTHING;

-- Insert sample follows
INSERT INTO public.follows (follower_id, following_id) VALUES
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0004'),
('********-0000-0000-0000-********0001', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-********0003', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0003', '********-0000-0000-0000-********0004'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0004', '********-0000-0000-0000-********0005'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0001'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0003'),
('********-0000-0000-0000-********0005', '********-0000-0000-0000-********0004')
ON CONFLICT (follower_id, following_id) DO NOTHING;
