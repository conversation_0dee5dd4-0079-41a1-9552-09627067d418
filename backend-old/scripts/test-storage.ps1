# Test script to verify storage initialization (PowerShell)
# This script checks if the media files are accessible via Supabase storage
# Tests the new structured format: user/{user-id}/{channel-id}/{filename}

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Storage Test Script" -ForegroundColor Green
    Write-Host "Usage: .\test-storage.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script tests if media files are accessible via Supabase storage."
    exit 0
}

Write-Host "🧪 Testing storage initialization..." -ForegroundColor Green

# Load environment variables from .env file
$envVars = @{}
if (Test-Path "..\\.env") {
    Get-Content "..\\.env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            $envVars[$matches[1]] = $matches[2]
        }
    }
}

# Default values if not set
$SUPABASE_URL = if ($envVars["SUPABASE_URL"]) { $envVars["SUPABASE_URL"] } else { "http://localhost:8000" }

# List of expected media files with their structured paths
$structuredFiles = @(
    "user/00000000-0000-0000-0000-000000000003/10000000-0000-0000-0000-000000000001/a1b2c3d4-e5f6-7890-abcd-ef1234567890.jpg",
    "user/00000000-0000-0000-0000-000000000004/10000000-0000-0000-0000-000000000003/b2c3d4e5-f6g7-8901-bcde-f23456789012.webp",
    "user/00000000-0000-0000-0000-000000000005/10000000-0000-0000-0000-000000000004/c3d4e5f6-g7h8-9012-cdef-345678901234.webp",
    "user/00000000-0000-0000-0000-000000000001/10000000-0000-0000-0000-000000000002/d4e5f6g7-h8i9-0123-def0-456789012345.png"
)

Write-Host "🔍 Checking if structured media files are accessible..." -ForegroundColor Yellow

$successCount = 0
$totalCount = $structuredFiles.Count

foreach ($filePath in $structuredFiles) {
    Write-Host "Testing: $filePath" -ForegroundColor Gray

    # Test public access to the file
    try {
        $url = "$SUPABASE_URL/storage/v1/object/public/media/$filePath"
        $response = Invoke-WebRequest -Uri $url -Method Head -TimeoutSec 10 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ $filePath is accessible" -ForegroundColor Green
            $successCount++
        }
        else {
            Write-Host "❌ $filePath is not accessible (HTTP $($response.StatusCode))" -ForegroundColor Red
        }
    }
    catch {
        $statusCode = if ($_.Exception.Response) { $_.Exception.Response.StatusCode } else { "Unknown" }
        Write-Host "❌ $filePath is not accessible (HTTP $statusCode)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "📊 Storage Test Results:" -ForegroundColor Cyan
Write-Host "   Accessible files: $successCount/$totalCount" -ForegroundColor White

if ($successCount -eq $totalCount) {
    Write-Host "✅ All media files are accessible!" -ForegroundColor Green
}
elseif ($successCount -gt 0) {
    Write-Host "⚠️  Some media files are not accessible" -ForegroundColor Yellow
}
else {
    Write-Host "❌ No media files are accessible" -ForegroundColor Red
}

Write-Host "🏁 Storage test completed!" -ForegroundColor Green
