# GameFlex Auth Users Initialization Script (PowerShell)
# This script creates development users using Supabase Auth API
# It runs after all services are up and the auth service is ready

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Auth Users Initialization Script" -ForegroundColor Green
    Write-Host "Usage: .\init-auth-users.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script creates development users in Supabase Auth for testing."
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🔐 Initializing GameFlex development users in Supabase Auth..." -ForegroundColor Green

# Load environment variables from .env file
$envPath = if (Test-Path ".env") { ".env" } elseif (Test-Path "..\\.env") { "..\\.env" } else { $null }

if (-not $envPath) {
    Write-Host "❌ .env file not found" -ForegroundColor Red
    exit 1
}

$envVars = @{}
Get-Content $envPath | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $envVars[$matches[1]] = $matches[2]
    }
}

# Set default ports if not specified
$KONG_HTTP_PORT = if ($envVars["KONG_HTTP_PORT"]) { $envVars["KONG_HTTP_PORT"] } else { "8000" }
$SERVICE_ROLE_KEY = $envVars["SERVICE_ROLE_KEY"]
$ANON_KEY = $envVars["ANON_KEY"]

# Wait for database to be ready
Write-Host "⏳ Waiting for database to be ready..." -ForegroundColor Yellow
$dbReady = $false
$attempts = 0
$maxAttempts = 30

while (-not $dbReady -and $attempts -lt $maxAttempts) {
    try {
        docker-compose exec -T db pg_isready -U postgres -h localhost 2>$null | Out-Null
        $dbReady = $LASTEXITCODE -eq 0
    }
    catch {
        $dbReady = $false
    }
    
    if (-not $dbReady) {
        Write-Host "Database is not ready yet, waiting..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($dbReady) {
    Write-Host "✅ Database is ready!" -ForegroundColor Green
}
else {
    Write-Host "❌ Database failed to start within expected time" -ForegroundColor Red
    exit 1
}

# Wait for auth service to be ready
Write-Host "⏳ Waiting for auth service to be ready..." -ForegroundColor Yellow
$authReady = $false
$attempts = 0

while (-not $authReady -and $attempts -lt $maxAttempts) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/settings" -Headers @{"apikey" = $ANON_KEY } -TimeoutSec 5 -UseBasicParsing
        $authReady = $response.StatusCode -eq 200
    }
    catch {
        $authReady = $false
    }
    
    if (-not $authReady) {
        Write-Host "Auth service is not ready yet, waiting..." -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($authReady) {
    Write-Host "✅ Auth service is ready!" -ForegroundColor Green
}
else {
    Write-Host "❌ Auth service failed to start within expected time" -ForegroundColor Red
    exit 1
}

# Function to create a user using Supabase Auth API
function Create-AuthUser {
    param(
        [string]$Email,
        [string]$Password,
        [string]$DisplayName
    )

    Write-Host "👤 Creating auth user: $Email" -ForegroundColor Yellow

    # Check if user already exists using Auth API
    try {
        $checkResponse = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/admin/users" -Headers @{
            "Authorization" = "Bearer $SERVICE_ROLE_KEY"
            "apikey"        = $SERVICE_ROLE_KEY
            "Content-Type"  = "application/json"
        } -UseBasicParsing -ErrorAction SilentlyContinue

        if ($checkResponse.StatusCode -eq 200) {
            $users = $checkResponse.Content | ConvertFrom-Json
            $existingUser = $users.users | Where-Object { $_.email -eq $Email }
            if ($existingUser) {
                Write-Host "   ✅ User $Email already exists" -ForegroundColor Green
                return $true
            }
        }
    }
    catch {
        Write-Host "   ⚠️  Could not check if user exists, proceeding with creation..." -ForegroundColor Yellow
    }

    # Create user using Supabase Auth API
    $userPayload = @{
        email         = $Email
        password      = $Password
        email_confirm = $true
        user_metadata = @{
            display_name = $DisplayName
        }
    } | ConvertTo-Json -Depth 3

    try {
        $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/admin/users" -Method POST -Headers @{
            "Authorization" = "Bearer $SERVICE_ROLE_KEY"
            "apikey"        = $SERVICE_ROLE_KEY
            "Content-Type"  = "application/json"
        } -Body $userPayload -UseBasicParsing -ErrorAction Stop

        if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 201) {
            $userInfo = $response.Content | ConvertFrom-Json
            Write-Host "   ✅ Successfully created user: $Email (ID: $($userInfo.id))" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "   ❌ Failed to create user: $Email (Status: $($response.StatusCode))" -ForegroundColor Red
            return $false
        }
    }
    catch {
        $errorMessage = $_.Exception.Message
        if ($_.Exception.Response) {
            try {
                $errorContent = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorContent)
                $errorBody = $reader.ReadToEnd()
                $errorJson = $errorBody | ConvertFrom-Json
                if ($errorJson.msg -match "already registered") {
                    Write-Host "   ✅ User $Email already exists" -ForegroundColor Green
                    return $true
                }
                else {
                    Write-Host "   ❌ Failed to create user: $Email - $($errorJson.msg)" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "   ❌ Failed to create user: $Email - $errorMessage" -ForegroundColor Red
            }
        }
        else {
            Write-Host "   ❌ Failed to create user: $Email - $errorMessage" -ForegroundColor Red
        }
        return $false
    }
}

# Create development users using Supabase Auth API
Write-Host "🔄 Creating development users..." -ForegroundColor Yellow
$users = @(
    @{ Email = "<EMAIL>"; Password = "DevPassword123!"; DisplayName = "Development User" },
    @{ Email = "<EMAIL>"; Password = "adminpassword123"; DisplayName = "Admin User" },
    @{ Email = "<EMAIL>"; Password = "johnpassword123"; DisplayName = "John Doe" },
    @{ Email = "<EMAIL>"; Password = "janepassword123"; DisplayName = "Jane Smith" },
    @{ Email = "<EMAIL>"; Password = "mikepassword123"; DisplayName = "Mike Wilson" }
)

$successCount = 0
foreach ($user in $users) {
    if (Create-AuthUser -Email $user.Email -Password $user.Password -DisplayName $user.DisplayName) {
        $successCount++
    }
}

Write-Host ""
Write-Host "✅ User creation completed using Supabase Auth API!" -ForegroundColor Green

# Verify users were created
Write-Host ""
Write-Host "🔍 Verifying auth users were created..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/auth/v1/admin/users" -Headers @{
        "Authorization" = "Bearer $SERVICE_ROLE_KEY"
        "apikey"        = $SERVICE_ROLE_KEY
    } -UseBasicParsing

    $userCount = ($response.Content | Select-String '"email"' -AllMatches).Matches.Count

    if ($userCount -ge 5) {
        Write-Host "✅ Successfully created/verified $userCount auth users!" -ForegroundColor Green
        Write-Host ""
        Write-Host "🎮 GameFlex Development Users:" -ForegroundColor Cyan
        Write-Host "   📧 <EMAIL> (password: DevPassword123!)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: adminpassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: johnpassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: janepassword123)" -ForegroundColor White
        Write-Host "   📧 <EMAIL> (password: mikepassword123)" -ForegroundColor White
        Write-Host ""
        Write-Host "🚀 You can now log in to GameFlex with any of these accounts!" -ForegroundColor Green
    }
    else {
        Write-Host "⚠️  Warning: Expected at least 5 users but found $userCount" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "⚠️  Warning: Could not verify user count via API" -ForegroundColor Yellow
}

Write-Host "✅ Auth users initialization complete!" -ForegroundColor Green
