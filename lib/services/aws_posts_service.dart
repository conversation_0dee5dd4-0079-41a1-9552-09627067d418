import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import '../models/post_model.dart';
import 'api_service.dart';
import 'aws_auth_service.dart';

/// AWS posts service for managing posts via API Gateway
class AwsPostsService {
  static AwsPostsService? _instance;
  static AwsPostsService get instance => _instance ??= AwsPostsService._();

  AwsPostsService._();

  /// Get posts with pagination
  Future<List<PostModel>> getPosts({
    int limit = 20,
    int offset = 0,
    String? channelId,
  }) async {
    print(
      '🚀 AwsPostsService.getPosts: Starting with limit=$limit, offset=$offset',
    );
    try {
      print('🔍 AwsPostsService.getPosts: Inside try block');
      developer.log(
        'AwsPostsService: Getting posts (limit: $limit, offset: $offset)',
      );

      print('🔍 AwsPostsService.getPosts: Building query params');
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      if (channelId != null) {
        queryParams['channel_id'] = channelId;
      }

      final queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final path = '/posts${queryString.isNotEmpty ? '?$queryString' : ''}';
      print('🔍 AwsPostsService.getPosts: Making API request to path: $path');

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: path,
      );
      print('🔍 AwsPostsService.getPosts: Received API response');

      print('🔍 AwsPostsService.getPosts: Parsing response');
      final data = ApiService.instance.parseResponse(response);
      print('🔍 AwsPostsService: Parsed response data keys: ${data.keys}');

      final postsData = data['posts'] as List<dynamic>? ?? [];
      print('🔍 AwsPostsService: Posts data type: ${postsData.runtimeType}');
      print('🔍 AwsPostsService: Posts data length: ${postsData.length}');

      print(
        '🔍 AwsPostsService: Processing ${postsData.length} posts from API',
      );

      final posts = <PostModel>[];
      for (int i = 0; i < postsData.length; i++) {
        print('🔍 AwsPostsService: Processing post $i');
        try {
          final postData = postsData[i] as Map<String, dynamic>;
          print('🔍 AwsPostsService: Post $i ID: ${postData['id']}');
          print(
            '🔍 AwsPostsService: Post $i media_url: ${postData['media_url']}',
          );

          print('🔍 AwsPostsService: Converting post $i to PostModel');
          final post = _convertToPostModel(postData);
          print('🔍 AwsPostsService: Successfully converted post $i');
          posts.add(post);
          print('🔍 AwsPostsService: Added post $i to list');
        } catch (e, stackTrace) {
          print('❌ AwsPostsService: Error processing post $i: $e');
          print('❌ AwsPostsService: Post data: ${postsData[i]}');
          print('❌ AwsPostsService: Stack trace: $stackTrace');
          // Continue processing other posts instead of failing completely
        }
      }

      print('🔍 AwsPostsService: Successfully processed all posts');
      print('🔍 AwsPostsService: Returning ${posts.length} posts');
      developer.log('AwsPostsService: Retrieved ${posts.length} posts');
      return posts;
    } catch (e, stackTrace) {
      print('❌ AwsPostsService: MAIN CATCH BLOCK - Error getting posts: $e');
      print('❌ AwsPostsService: MAIN CATCH BLOCK - Stack trace: $stackTrace');
      developer.log('AwsPostsService: Error getting posts: $e');
      throw Exception('Failed to get posts: $e');
    }
  }

  /// Get a specific post by ID
  Future<PostModel?> getPost(String postId) async {
    try {
      developer.log('AwsPostsService: Getting post $postId');

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/posts/$postId',
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Retrieved post $postId');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error getting post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return null;
    }
  }

  /// Create a new post
  Future<PostModel?> createPost({
    required String content,
    String? channelId,
    String? mediaId,
  }) async {
    try {
      developer.log('AwsPostsService: Creating post');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final body = <String, dynamic>{'content': content};

      if (channelId != null) {
        body['channel_id'] = channelId;
      }

      if (mediaId != null) {
        body['media_id'] = mediaId;
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/posts',
        body: body,
        accessToken: accessToken,
      );

      final data = ApiService.instance.parseResponse(response);
      final postData = data['post'] as Map<String, dynamic>?;

      if (postData != null) {
        final post = _convertToPostModel(postData);
        developer.log('AwsPostsService: Created post ${post.id}');
        return post;
      }

      return null;
    } catch (e) {
      developer.log('AwsPostsService: Error creating post: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      throw Exception('Failed to create post: $e');
    }
  }

  /// Like a post
  Future<bool> likePost(String postId) async {
    try {
      developer.log('AwsPostsService: Liking post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'POST',
        path: '/posts/$postId/like',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Liked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error liking post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return false;
    }
  }

  /// Unlike a post
  Future<bool> unlikePost(String postId) async {
    try {
      developer.log('AwsPostsService: Unliking post $postId');

      final accessToken = AwsAuthService.instance.accessToken;
      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'DELETE',
        path: '/posts/$postId/like',
        accessToken: accessToken,
      );

      ApiService.instance.parseResponse(response);
      developer.log('AwsPostsService: Unliked post $postId');
      return true;
    } catch (e) {
      developer.log('AwsPostsService: Error unliking post $postId: $e');
      if (kDebugMode) {
        print('AwsPostsService ERROR: $e');
      }
      return false;
    }
  }

  /// Convert AWS API response to PostModel
  PostModel _convertToPostModel(Map<String, dynamic> data) {
    try {
      print('🔧 _convertToPostModel: Converting post ${data['id']}');
      print('🔧 _convertToPostModel: Raw data: $data');

      // Use PostModel.fromJson for consistent parsing
      final post = PostModel.fromJson(data);
      print(
        '🔧 _convertToPostModel: Created post with mediaUrl: ${post.mediaUrl}',
      );
      return post;
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ _convertToPostModel: Error converting post: $e');
        print('❌ _convertToPostModel: Data: $data');
        print('❌ _convertToPostModel: Stack trace: $stackTrace');
      }
      rethrow;
    }
  }

  /// Construct media URL from S3 bucket and key
  String constructMediaUrl(String bucket, String key) {
    // For LocalStack development, construct URL to LocalStack S3
    // TODO: Make this environment-aware for production
    return 'http://localhost:45660/$bucket/$key';
  }
}
