import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'api_service.dart';

/// AWS User Service for handling user profile operations
class AwsUserService {
  static final AwsUserService _instance = AwsUserService._internal();
  static AwsUserService get instance => _instance;
  AwsUserService._internal();

  /// Get user profile by user ID
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    try {
      developer.log('AwsUserService: Getting user profile for ID: $userId');
      if (kDebugMode) {
        print('AwsUserService: Getting user profile for ID: $userId');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/users/profile/$userId',
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully retrieved user profile');
      if (kDebugMode) {
        print('AwsUserService: Successfully retrieved user profile');
      }

      return data;
    } catch (e) {
      developer.log('AwsUserService: Error getting user profile: $e');
      if (kDebugMode) {
        print('AwsUserService: Error getting user profile: $e');
      }
      return null;
    }
  }

  /// Get user posts by user ID
  Future<List<Map<String, dynamic>>> getUserPosts(
    String userId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      developer.log('AwsUserService: Getting user posts for ID: $userId');
      if (kDebugMode) {
        print('AwsUserService: Getting user posts for ID: $userId');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/users/posts/$userId?limit=$limit&offset=$offset',
      );

      final data = ApiService.instance.parseResponse(response);
      final posts = data['posts'] as List<dynamic>? ?? [];

      developer.log(
        'AwsUserService: Successfully retrieved ${posts.length} user posts',
      );
      if (kDebugMode) {
        print(
          'AwsUserService: Successfully retrieved ${posts.length} user posts',
        );
      }

      return posts.cast<Map<String, dynamic>>();
    } catch (e) {
      developer.log('AwsUserService: Error getting user posts: $e');
      if (kDebugMode) {
        print('AwsUserService: Error getting user posts: $e');
      }
      return [];
    }
  }

  /// Update current user's profile
  Future<bool> updateUserProfile({
    String? displayName,
    String? username,
    String? bio,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? country,
    String? timezone,
    String? language,
  }) async {
    try {
      developer.log('AwsUserService: Updating user profile');
      if (kDebugMode) {
        print('AwsUserService: Updating user profile');
      }

      final body = <String, dynamic>{};

      // User table fields
      if (displayName != null) body['display_name'] = displayName;
      if (username != null) body['username'] = username;
      if (bio != null) body['bio'] = bio;
      if (avatarUrl != null) body['avatar_url'] = avatarUrl;

      // User profile table fields
      if (firstName != null) body['first_name'] = firstName;
      if (lastName != null) body['last_name'] = lastName;
      if (country != null) body['country'] = country;
      if (timezone != null) body['timezone'] = timezone;
      if (language != null) body['language'] = language;

      final response = await ApiService.instance.makeRequest(
        method: 'PUT',
        path: '/users/profile',
        body: body,
      );

      final data = ApiService.instance.parseResponse(response);

      developer.log('AwsUserService: Successfully updated user profile');
      if (kDebugMode) {
        print('AwsUserService: Successfully updated user profile');
      }

      return true;
    } catch (e) {
      developer.log('AwsUserService: Error updating user profile: $e');
      if (kDebugMode) {
        print('AwsUserService: Error updating user profile: $e');
      }
      return false;
    }
  }

  /// Get user statistics
  Future<Map<String, int>> getUserStats(String userId) async {
    try {
      developer.log('AwsUserService: Getting user stats for ID: $userId');
      if (kDebugMode) {
        print('AwsUserService: Getting user stats for ID: $userId');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/users/profile/$userId',
      );

      final data = ApiService.instance.parseResponse(response);
      final stats = data['stats'] as Map<String, dynamic>? ?? {};

      final userStats = {
        'posts': stats['posts'] as int? ?? 0,
        'followers': stats['followers'] as int? ?? 0,
        'following': stats['following'] as int? ?? 0,
        'likes': stats['likes'] as int? ?? 0,
      };

      developer.log('AwsUserService: Successfully retrieved user stats');
      if (kDebugMode) {
        print('AwsUserService: Successfully retrieved user stats: $userStats');
      }

      return userStats;
    } catch (e) {
      developer.log('AwsUserService: Error getting user stats: $e');
      if (kDebugMode) {
        print('AwsUserService: Error getting user stats: $e');
      }
      return {'posts': 0, 'followers': 0, 'following': 0, 'likes': 0};
    }
  }

  /// Get basic user info (user + profile combined)
  Future<Map<String, dynamic>?> getUserInfo(String userId) async {
    try {
      developer.log('AwsUserService: Getting user info for ID: $userId');
      if (kDebugMode) {
        print('AwsUserService: Getting user info for ID: $userId');
      }

      final response = await ApiService.instance.makeRequest(
        method: 'GET',
        path: '/users/profile/$userId',
      );

      final data = ApiService.instance.parseResponse(response);

      // Combine user and profile data
      final user = data['user'] as Map<String, dynamic>? ?? {};
      final profile = data['profile'] as Map<String, dynamic>? ?? {};
      final stats = data['stats'] as Map<String, dynamic>? ?? {};

      final combinedInfo = <String, dynamic>{
        ...user,
        ...profile,
        'stats': stats,
      };

      developer.log('AwsUserService: Successfully retrieved user info');
      if (kDebugMode) {
        print('AwsUserService: Successfully retrieved user info');
      }

      return combinedInfo;
    } catch (e) {
      developer.log('AwsUserService: Error getting user info: $e');
      if (kDebugMode) {
        print('AwsUserService: Error getting user info: $e');
      }
      return null;
    }
  }
}
