import 'package:flutter/foundation.dart';

class PostModel {
  final String id;
  final String userId;
  final String? channelId;
  final String content;
  final String? mediaUrl;
  final String? mediaType;
  final String? mediaId;
  final Map<String, dynamic>? mediaData;
  final int likeCount;
  final int commentCount;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  // User information (from join)
  final String? username;
  final String? displayName;
  final String? avatarUrl;

  // Like status for current user
  final bool isLikedByCurrentUser;

  PostModel({
    required this.id,
    required this.userId,
    this.channelId,
    required this.content,
    this.mediaUrl,
    this.mediaType,
    this.mediaId,
    this.mediaData,
    required this.likeCount,
    required this.commentCount,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.username,
    this.displayName,
    this.avatarUrl,
    this.isLikedByCurrentUser = false,
  });

  factory PostModel.fromJson(Map<String, dynamic> json) {
    print('🔍 PostModel.fromJson: Starting to parse post ${json['id']}');
    try {
      print('🔍 PostModel.fromJson: Inside try block for post ${json['id']}');
      print('🔍 PostModel.fromJson: Raw media_url: ${json['media_url']}');
      print('🔍 PostModel.fromJson: Raw media_type: ${json['media_type']}');
      print('🔍 PostModel.fromJson: Raw media: ${json['media']}');

      // Handle both old and new media formats
      print('🔍 PostModel.fromJson: Parsing media information');
      String? mediaUrl;
      String? mediaType;
      final mediaId = json['media_id'] as String?;
      final mediaData = json['media'] as Map<String, dynamic>?;

      // Handle media URL and type
      if (mediaData != null) {
        print('🔍 PostModel.fromJson: Found media object');
        // For AWS backend, media URLs are already complete
        mediaUrl = mediaData['url'] as String?;
        mediaType = mediaData['type'] as String?;
        print(
          '🔍 PostModel.fromJson: Using media object - URL: $mediaUrl, Type: $mediaType',
        );
      } else {
        print('🔍 PostModel.fromJson: No media object, using direct fields');
        // Fall back to direct URL
        mediaUrl = json['media_url'] as String?;
        mediaType = json['media_type'] as String?;
        print(
          '🔍 PostModel.fromJson: Using direct fields - URL: $mediaUrl, Type: $mediaType',
        );
      }

      print(
        '🔍 PostModel.fromJson: Creating PostModel with mediaUrl: $mediaUrl',
      );
      final postModel = PostModel(
        id: json['id'] as String,
        userId: json['user_id'] as String,
        channelId: json['channel_id'] as String?,
        content: json['content'] as String? ?? '',
        mediaUrl: mediaUrl,
        mediaType: mediaType,
        mediaId: mediaId,
        mediaData: mediaData,
        likeCount: json['like_count'] as int? ?? 0,
        commentCount: json['comment_count'] as int? ?? 0,
        isActive: json['is_active'] as bool? ?? true,
        createdAt: DateTime.parse(json['created_at'] as String),
        updatedAt: DateTime.parse(json['updated_at'] as String),
        username: json['username'] as String?,
        displayName: json['display_name'] as String?,
        avatarUrl: json['avatar_url'] as String?,
        isLikedByCurrentUser:
            json['is_liked_by_current_user'] as bool? ?? false,
      );
      print(
        '🔍 PostModel.fromJson: Successfully created PostModel with mediaUrl: ${postModel.mediaUrl}',
      );
      return postModel;
    } catch (e, stackTrace) {
      print('❌ PostModel.fromJson: Error parsing post: $e');
      print('❌ PostModel.fromJson: JSON: $json');
      print('❌ PostModel.fromJson: Stack trace: $stackTrace');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'channel_id': channelId,
      'content': content,
      'media_url': mediaUrl,
      'media_type': mediaType,
      'like_count': likeCount,
      'comment_count': commentCount,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'username': username,
      'display_name': displayName,
      'avatar_url': avatarUrl,
      'is_liked_by_current_user': isLikedByCurrentUser,
    };
  }

  PostModel copyWith({
    String? id,
    String? userId,
    String? channelId,
    String? content,
    String? mediaUrl,
    String? mediaType,
    String? mediaId,
    Map<String, dynamic>? mediaData,
    int? likeCount,
    int? commentCount,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? username,
    String? displayName,
    String? avatarUrl,
    bool? isLikedByCurrentUser,
  }) {
    return PostModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      channelId: channelId ?? this.channelId,
      content: content ?? this.content,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaType: mediaType ?? this.mediaType,
      mediaId: mediaId ?? this.mediaId,
      mediaData: mediaData ?? this.mediaData,
      likeCount: likeCount ?? this.likeCount,
      commentCount: commentCount ?? this.commentCount,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      username: username ?? this.username,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isLikedByCurrentUser: isLikedByCurrentUser ?? this.isLikedByCurrentUser,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 7) {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get authorDisplayName {
    if (displayName != null && displayName!.isNotEmpty) {
      return displayName!;
    }
    if (username != null && username!.isNotEmpty) {
      return username!;
    }
    return 'User';
  }

  String get authorUsername {
    if (username != null && username!.isNotEmpty) {
      return '@${username!}';
    }
    return '@user';
  }

  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;

  bool get isImage => mediaType == 'image';

  bool get isVideo => mediaType == 'video';

  @override
  String toString() {
    return 'PostModel(id: $id, content: $content, author: $authorDisplayName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PostModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
